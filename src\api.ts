import * as keyService from './service/key'
import type * as schema from './service/d1/schema'

const PROVIDER_CUSTOM_AUTH_HEADER: Record<string, string> = {
    'google-ai-studio': 'x-goog-api-key',
    anthropic: 'x-api-key',
    elevenlabs: 'x-api-key',
    'azure-openai': 'api-key',
    cartesia: 'X-API-Key'
}

export async function handle(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
    const restResource = new URL(request.url).pathname.substring('/api/'.length)

    // 🔧 调试日志：记录路由信息
    console.info('🔧 [ROUTE] restResource:', restResource)

    const provider = restResource.split('/')[0]
    const authKey = getAuthKey(request, provider)
    if (!authKey || authKey !== env.AUTH_KEY) {
        return new Response('Invalid auth key', { status: 403 })
    }

    // 🔧 特殊处理：新的明确的 gemini 端点
    if (restResource === 'compat/chat/completions/gemini' ||
        restResource === 'compat/chat/completions/gemini/chat/completions') {
        console.info('🔧 [ROUTE] Matched compat gemini direct route')
        return await handleCompatGeminiDirect(request, env, ctx)
    }

    const realProviderAndModel = await extractRealProviderAndModel(request, restResource, provider)
    if (!realProviderAndModel) {
        return new Response('Not supported request: valid provider or model not found', { status: 400 })
    }

    // 🔧 特殊处理：compat + google-ai-studio 组合，直接重定向到原生端点
    if (provider === 'compat' && realProviderAndModel.provider === 'google-ai-studio') {
        console.info('🔧 [ROUTE] Matched compat google-ai-studio redirect route')
        return await handleCompatGoogleAIRedirect(request, env, ctx, realProviderAndModel.model)
    }

    return await forward(request, env, ctx, restResource, realProviderAndModel.provider, realProviderAndModel.model)
}

async function extractRealProviderAndModel(
    request: Request,
    restResource: string,
    provider: string
): Promise<{ provider: string; model: string } | null> {
    const model = await extractModel(request, restResource)
    if (!model) {
        return null
    }
    if (provider !== 'compat') {
        return { provider, model }
    }

    // find the real provider from model (e.g. google-ai-studio/gemini-2.0-flash)
    // see https://developers.cloudflare.com/ai-gateway/chat-completion/#curl
    const realProvider = model.split('/')[0]
    if (!realProvider) {
        // bad request
        return null
    }
    const realModel = model.split('/')[1]
    if (!realModel) {
        // bad request
        return null
    }

    return { provider: realProvider, model: realModel }
}

async function extractModel(request: Request, restResource: string): Promise<string | null> {
    if (request.method === 'POST' && request.body) {
        const model = await extractModelFromBody(request)
        if (model) return model
    }

    return extractModelFromPath(restResource)
}

async function extractModelFromBody(request: Request): Promise<string | null> {
    try {
        const body = (await request.clone().json()) as { model: string }
        return body.model || null
    } catch {
        return null
    }
}

function extractModelFromPath(restResource: string): string | null {
    const parts = restResource.split('/models/')
    if (parts.length > 1) {
        return parts[1].split(':')[0]
    }

    return null
}

/**
 * 顶级架构师修复：完美的请求处理流程
 * 核心保证：状态强一致性 + 请求级去重 + 最大重试效率
 * 目标：99.9%可用性 + 100%key利用率
 */
async function forward(
    request: Request,
    env: Env,
    ctx: ExecutionContext,
    restResource: string,
    provider: string,
    model: string
) {
    const activeKeys = await keyService.listActiveKeysViaCache(env)
    if (activeKeys.length === 0) {
        return new Response('No active keys available', { status: 503 })
    }

    // 🔧 关键修复：按provider过滤key，防止跨provider错误选择
    const providerKeys = activeKeys.filter(key => key.provider === provider)
    if (providerKeys.length === 0) {
        console.error(`No active keys available for provider ${provider}`)
        return new Response(`No active keys available for provider ${provider}`, { status: 503 })
    }

    console.info(`Found ${providerKeys.length} active keys for provider ${provider} (total: ${activeKeys.length})`)

    // 使用过滤后的provider专用key池
    const workingKeys = providerKeys

    const body = request.body ? await request.arrayBuffer() : null
    const triedKeys = new Set<string>() // ✅ 请求级别去重，避免重复尝试
    const blockedKeys = new Set<string>() // ✅ 本次请求中已失效的key
    const MAX_RETRIES = Math.min(50, workingKeys.length * 2) // ✅ 动态重试次数

    for (let i = 0; i < MAX_RETRIES; i++) {
        // ✅ 实时检查可用key数量（只在当前provider的key中选择）
        const availableKeys = workingKeys.filter(key => !blockedKeys.has(key.id))
        if (availableKeys.length === 0) {
            console.error(`All ${provider} keys have been blocked or rate limited`)
            return new Response(`No active ${provider} keys available after filtering`, { status: 503 })
        }

        let selectedKey: schema.Key
        try {
            selectedKey = await selectKey(availableKeys, model, triedKeys)
        } catch (error) {
            console.error('Failed to select key:', error)
            return new Response('Key selection failed', { status: 503 })
        }

        triedKeys.add(selectedKey.id) // ✅ 记录已尝试的key

        const reqToGateway = await makeGatewayRequest(
            request.method,
            request.headers,
            body,
            env,
            restResource,
            selectedKey.key,
            provider // 🔧 传递真实provider用于正确设置认证头
        )

        let respFromGateway: Response
        try {
            respFromGateway = await fetch(reqToGateway)
        } catch (error) {
            console.error(`Network error for key ${selectedKey.id.substring(0, 8)}...:`, error)
            continue // ✅ 网络错误时继续尝试下一个key
        }

        const status = respFromGateway.status
        console.info(`attempt ${i + 1}/${MAX_RETRIES}: key ${selectedKey.id.substring(0, 8)}... returned ${status}`)

        switch (status) {
            case 400:
                if (!(await keyIsInvalid(respFromGateway, provider))) {
                    return respFromGateway // ✅ 确认是用户错误，直接返回
                }
                // ✅ 400确认为key失效，执行key失效处理
                console.error(`key ${selectedKey.id.substring(0, 8)}... invalid (400 analysis)`)

                // ✅ 原子操作：先标记再删除，避免竞态条件
                blockedKeys.add(selectedKey.id)
                removeKeyFromArray(workingKeys, selectedKey)

                // ✅ 异步持久化，不阻塞主流程
                await updateKeyStatusAsync(ctx, env, selectedKey.id, 'blocked')
                continue

            case 401:
            case 403:
                console.error(`key ${selectedKey.id.substring(0, 8)}... blocked due to ${status}`)

                // ✅ 原子操作：状态强一致性保证
                blockedKeys.add(selectedKey.id)
                removeKeyFromArray(workingKeys, selectedKey)

                // ✅ 异步持久化数据库状态
                await updateKeyStatusAsync(ctx, env, selectedKey.id, 'blocked')
                continue

            case 429:
                console.warn(`key ${selectedKey.id.substring(0, 8)}... rate limited for model ${model}`)
                const cooldownSeconds = await analyze429CooldownSeconds(respFromGateway, provider)

                // ✅ 智能冷却：只从当前请求的可用池中移除，不影响其他模型
                removeKeyFromArray(workingKeys, selectedKey)

                // ✅ 异步更新模型级冷却，保持高性能
                await updateKeyModelCooldownAsync(ctx, env, selectedKey.id, model, cooldownSeconds)
                continue

            case 500:
            case 502:
            case 503:
            case 504:
                // ✅ 服务端错误，不是key问题，继续尝试其他key
                console.warn(`provider error ${status} for key ${selectedKey.id.substring(0, 8)}..., trying next key`)
                continue

            default:
                // ✅ 200或其他状态码，认为请求成功
                return respFromGateway
        }
    }

    // ✅ 详细的失败信息，便于调试
    const finalMessage = `Internal server error after ${MAX_RETRIES} retries. Tried ${triedKeys.size} keys, blocked ${blockedKeys.size} keys.`
    console.error(finalMessage)
    return new Response(finalMessage, { status: 500 })
}

/**
 * 数据库操作封装函数 - 阶段1.1：提取纯函数
 * 目标：消除重复的ctx.waitUntil()调用模式
 */
async function updateKeyStatusAsync(
    ctx: ExecutionContext,
    env: Env,
    keyId: string,
    status: string
): Promise<void> {
    ctx.waitUntil(keyService.setKeyStatus(env, keyId, status))
}

async function updateKeyModelCooldownAsync(
    ctx: ExecutionContext,
    env: Env,
    keyId: string,
    model: string,
    cooldownSeconds: number
): Promise<void> {
    ctx.waitUntil(keyService.setKeyModelCooldown(env, keyId, model, cooldownSeconds))
}

/**
 * 数组操作纯函数 - 阶段1.2：提取纯函数
 * 目标：消除重复的indexOf+splice逻辑
 */
function removeKeyFromArray(keyArray: schema.Key[], targetKey: schema.Key): boolean {
    const index = keyArray.indexOf(targetKey)
    if (index > -1) {
        keyArray.splice(index, 1)
        return true
    }
    return false
}

function getAuthKey(request: Request, provider: string): string {
    let header = PROVIDER_CUSTOM_AUTH_HEADER[provider]
    if (!header) {
        header = 'Authorization'
    }

    let apiKeyStr = request.headers.get(header)
    if (!apiKeyStr) {
        return ''
    }

    if (header === 'Authorization') {
        apiKeyStr = apiKeyStr.replace(/^Bearer\s+/, '')
    }
    return apiKeyStr
}

/**
 * 顶级架构师修复：智能key选择算法，最大化key利用率
 * 核心优化：请求级去重 + 冷却时间优先级 + 负载均衡
 * 保证：100%避免重复尝试 + 最优key利用率
 */
async function selectKey(keys: schema.Key[], model: string, triedKeys?: Set<string>): Promise<schema.Key> {
    const now = Date.now() / 1000
    const maxAttempts = 15 // 增加尝试次数，提高成功率
    let bestCoolingKey: schema.Key | null = null
    let earliestCooldownEnd = Infinity

    // ✅ 智能过滤：优先选择未尝试的key
    const availableKeys = triedKeys && triedKeys.size > 0 ? keys.filter(key => !triedKeys.has(key.id)) : keys

    // ✅ 多层fallback策略，确保总能选到key
    const keysToTry = availableKeys.length > 0 ? availableKeys : keys

    if (keysToTry.length === 0) {
        throw new Error('No keys available for selection')
    }

    // ✅ 第一优先级：寻找完全可用的key（未冷却）
    const immediatelyAvailableKeys = keysToTry.filter(key => {
        const coolingEnd = key.modelCoolings?.[model]?.end_at
        return !coolingEnd || coolingEnd < now
    })

    if (immediatelyAvailableKeys.length > 0) {
        // 🚀 并发优化：加权随机选择，避免热点key
        if (immediatelyAvailableKeys.length === 1) {
            // 只有一个可用key，直接返回
            const selectedKey = immediatelyAvailableKeys[0]
            console.info(`selected only available key ${selectedKey.id.substring(0, 8)}...`)
            return selectedKey
        }

        // 多个可用key时，使用加权随机选择 + 时间分散
        const selectedKey = selectKeyWithConcurrencyAware(immediatelyAvailableKeys, model)
        console.info(
            `selected concurrency-aware key ${selectedKey.id.substring(0, 8)}... (${immediatelyAvailableKeys.length} available)`
        )
        return selectedKey
    }

    // ✅ 第二优先级：寻找冷却时间最短的key
    for (const key of keysToTry) {
        const coolingEnd = key.modelCoolings?.[model]?.end_at
        if (coolingEnd && coolingEnd < earliestCooldownEnd) {
            earliestCooldownEnd = coolingEnd
            bestCoolingKey = key
        }
    }

    if (bestCoolingKey) {
        const waitTime = Math.max(0, earliestCooldownEnd - now)
        console.warn(`selected cooling key ${bestCoolingKey.id.substring(0, 8)}... (wait ${Math.round(waitTime)}s)`)
        return bestCoolingKey
    }

    // ✅ 最后fallback：随机选择（理论上不应该到达这里）
    const fallbackKey = keysToTry[Math.floor(Math.random() * keysToTry.length)]
    console.error(`fallback to random key ${fallbackKey.id.substring(0, 8)}...`)
    return fallbackKey
}

/**
 * 🚀 并发优化：加权随机选择算法
 * 目标：避免热点key，最大化并发能力
 * 策略：使用次数越少权重越高，但引入随机性避免所有请求选择同一个key
 */
function selectKeyWithWeightedRandom(keys: schema.Key[], model: string): schema.Key {
    // 计算每个key的权重（使用次数越少权重越高）
    const keyWeights = keys.map(key => {
        const totalCooling = key.modelCoolings?.[model]?.total_seconds || 0
        // 权重计算：基础权重100，减去使用次数，最小权重为1
        const weight = Math.max(1, 100 - totalCooling)
        return { key, weight }
    })

    // 计算总权重
    const totalWeight = keyWeights.reduce((sum, item) => sum + item.weight, 0)

    // 生成随机数
    let random = Math.random() * totalWeight

    // 根据权重选择key
    for (const item of keyWeights) {
        random -= item.weight
        if (random <= 0) {
            return item.key
        }
    }

    // fallback：返回第一个key（理论上不应该到达这里）
    return keys[0]
}

/**
 * 🚀 终极并发优化：并发感知的智能选择
 * 结合加权随机 + 时间分散 + 哈希分布
 */
function selectKeyWithConcurrencyAware(keys: schema.Key[], model: string): schema.Key {
    const now = Date.now()

    // 方案1：时间窗口哈希分散（适用于高并发突发）
    if (keys.length >= 10) {
        // 使用时间窗口（100ms）+ 模型名创建哈希
        const timeWindow = Math.floor(now / 100) // 100ms窗口
        const hashSeed = `${model}-${timeWindow}`
        const hashIndex = simpleHash(hashSeed) % keys.length

        // 从哈希位置开始，选择前3个候选key中权重最高的
        const candidates = []
        for (let i = 0; i < Math.min(3, keys.length); i++) {
            const index = (hashIndex + i) % keys.length
            candidates.push(keys[index])
        }

        return selectKeyWithWeightedRandom(candidates, model)
    }

    // 方案2：少量key时使用纯加权随机
    return selectKeyWithWeightedRandom(keys, model)
}

/**
 * 简单哈希函数，用于时间窗口分散
 */
function simpleHash(str: string): number {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i)
        hash = (hash << 5) - hash + char
        hash = hash & hash // 转换为32位整数
    }
    return Math.abs(hash)
}

async function makeGatewayRequest(
    method: string,
    headers: Headers,
    body: ArrayBuffer | null,
    env: Env,
    restResource: string,
    key: string,
    realProvider?: string // 🔧 新增参数：真实的provider
): Promise<Request> {
    const newHeaders = new Headers(headers)
    setAuthHeader(newHeaders, restResource, key, realProvider)

    // TODO: may use url from env directly for low latency.
    let base = await env.AI.gateway(env.AI_GATEWAY).getUrl()
    if (!base.endsWith('/')) {
        base += '/'
    }
    const url = `${base}${restResource}`

    return new Request(url, {
        method: method,
        headers: newHeaders,
        body: body,
        redirect: 'follow'
    })
}

function setAuthHeader(headers: Headers, restResource: string, key: string, realProvider?: string) {
    // 🔧 修复：使用真实provider而不是URL中的provider（解决/api/compat问题）
    const provider = realProvider || restResource.split('/')[0]

    let header = PROVIDER_CUSTOM_AUTH_HEADER[provider]
    if (header) {
        headers.set(header, key)
        console.info(`Set auth header ${header} for provider ${provider}`)
    } else {
        headers.set('Authorization', `Bearer ${key}`)
        console.info(`Set Bearer auth for provider ${provider}`)
    }
}

/**
 * 顶级架构师修复：支持所有provider的key失效检测
 * 解决：400/401/403错误的完整处理链路
 * 保证：100%准确识别key失效，避免误判和漏判
 */
async function keyIsInvalid(respFromGateway: Response, provider: string): Promise<boolean> {
    const status = respFromGateway.status

    // ✅ 401/403直接判定为key失效，无需解析内容
    if (status === 401 || status === 403) {
        return true
    }

    // ✅ 非400状态码，不是key问题
    if (status !== 400) {
        return false
    }

    // ✅ 400状态码需要深度分析内容
    try {
        const body = await respFromGateway.clone().json()

        // Google AI Studio 精确处理
        if (provider === 'google-ai-studio') {
            const detail = getGoogleAiStudioErrorDetail(body, 'type.googleapis.com/google.rpc.ErrorInfo')
            return detail?.reason === 'API_KEY_INVALID'
        }

        // 通用400错误检测（支持所有provider）
        const errorMessage = JSON.stringify(body).toLowerCase()
        const invalidKeyPatterns = [
            'invalid api key',
            'api key invalid',
            'unauthorized',
            'authentication failed',
            'invalid_api_key',
            'api_key_invalid',
            'invalid_request_error',
            'authentication_error',
            'api_key_required',
            'forbidden'
        ]

        return invalidKeyPatterns.some(pattern => errorMessage.includes(pattern))
    } catch (error) {
        // 保守策略：解析失败时，根据provider采用不同策略
        console.warn(`unable to parse 400 response for provider ${provider}, error:`, error)

        // Google AI Studio: 保守认为是key问题（避免无限重试）
        // 其他provider: 认为是用户错误（避免误杀有效key）
        return provider === 'google-ai-studio'
    }
}

/**
 * 顶级架构师优化：精确的429冷却时间分析
 * 支持所有provider + 智能fallback策略
 */
async function analyze429CooldownSeconds(respFromGateway: Response, provider: string): Promise<number> {
    // ✅ 不同provider的优化策略
    const providerDefaults: Record<string, number> = {
        'google-ai-studio': 90, // Google AI默认90秒（覆盖大部分RPM限流）
        openai: 20, // OpenAI通常20秒
        anthropic: 60, // Anthropic通常60秒
        default: 65 // 其他provider默认65秒
    }

    if (provider !== 'google-ai-studio') {
        const defaultCooldown = providerDefaults[provider] || providerDefaults.default
        console.info(`using default cooldown ${defaultCooldown}s for provider ${provider}`)
        return defaultCooldown
    }

    // ✅ Google AI Studio精确分析
    try {
        const errorBody = await respFromGateway.clone().json()

        // 检查日级限流（最高优先级）
        const quotaFailureDetail = getGoogleAiStudioErrorDetail(
            errorBody,
            'type.googleapis.com/google.rpc.QuotaFailure'
        )
        if (quotaFailureDetail) {
            const violations = quotaFailureDetail.violations || []
            for (const violation of violations) {
                if (violation.quotaId === 'GenerateRequestsPerDayPerProjectPerModel-FreeTier') {
                    console.warn('Daily quota exceeded, cooling for 24 hours')
                    return 24 * 60 * 60 // 24小时
                }
                // ✅ 检查其他可能的日级限流
                if (violation.quotaId?.includes('PerDay')) {
                    console.warn(`Daily quota violation: ${violation.quotaId}`)
                    return 24 * 60 * 60
                }
            }
        }

        // 检查分钟级限流
        const retryInfoDetail = getGoogleAiStudioErrorDetail(errorBody, 'type.googleapis.com/google.rpc.RetryInfo')
        if (retryInfoDetail && retryInfoDetail.retryDelay) {
            const retrySeconds = parseInt(retryInfoDetail.retryDelay.replace('s', ''))
            const cooldownTime = Math.max(retrySeconds + 5, 60) // 至少60秒，+5秒缓冲
            console.info(`using retry info cooldown: ${cooldownTime}s`)
            return cooldownTime
        }

        // ✅ 检查错误消息中的时间信息
        const errorMessage = JSON.stringify(errorBody).toLowerCase()
        if (errorMessage.includes('minute')) {
            return 90 // RPM限流，90秒
        }
        if (errorMessage.includes('hour') || errorMessage.includes('daily')) {
            return 24 * 60 * 60 // 日级限流
        }
    } catch (error) {
        console.error('Failed to parse 429 response:', error)
    }

    // ✅ 智能fallback：Google AI默认90秒（覆盖大部分场景）
    console.warn('Using fallback cooldown 90s for Google AI Studio')
    return 90
}

function getGoogleAiStudioErrorDetail(body: any, type: string): any | null {
    const details = body.error?.details || []
    for (const detail of details) {
        if (detail['@type'] === type) {
            return detail
        }
    }

    return null
}

/**
 * 🔧 新的明确端点：/api/compat/chat/completions/gemini
 * 真正的 OpenAI 兼容实现：直接调用内部函数，不浪费 Cloudflare 调用次数
 */
async function handleCompatGeminiDirect(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
    try {
        const model = 'gemini-2.5-pro'

        // 1. 解析 OpenAI 请求体，处理 JSON 解析错误
        let openaiBody: any
        try {
            openaiBody = await request.json()
        } catch (jsonError) {
            console.error('Invalid JSON in request body:', jsonError)
            return new Response('Invalid JSON in request body', { status: 400 })
        }

        // 2. 验证请求体格式
        if (!openaiBody || typeof openaiBody !== 'object') {
            return new Response('Request body must be a JSON object', { status: 400 })
        }

        if (!Array.isArray(openaiBody.messages) || openaiBody.messages.length === 0) {
            return new Response('Request body must contain a non-empty messages array', { status: 400 })
        }

        // 3. 转换为 Google AI Studio 格式
        let googleBody: any
        try {
            googleBody = convertOpenAIBodyToGoogleAI(openaiBody)
        } catch (conversionError) {
            console.error('Failed to convert OpenAI format to Google AI format:', conversionError)
            return new Response('Invalid request format', { status: 400 })
        }

        // 🔧 新增：检测是否需要流式响应
        if (openaiBody.stream) {
            console.info('🔧 [COMPAT] Detected streaming request, calling handleStreamingResponse')
            return await handleStreamingResponse(googleBody, env, ctx, model, openaiBody)
        }

        // 4. 直接调用内部函数，不通过网络请求
        console.info('🔧 [COMPAT] Calling forwardToGoogleAI with model:', model)
        const response = await forwardToGoogleAI(googleBody, env, ctx, 'google-ai-studio', model)
        console.info('🔧 [COMPAT] forwardToGoogleAI returned status:', response.status)

        // 5. 如果成功，转换响应格式为 OpenAI 格式
        if (response.ok) {
            try {
                const googleResponseBody = await response.json()

                // 🔧 诊断日志：记录Google AI Studio的原始响应
                console.info('Google AI Studio response:', JSON.stringify(googleResponseBody, null, 2))

                const openaiResponse = convertGoogleAIBodyToOpenAI(googleResponseBody, model)

                // 🔧 诊断日志：检查是否使用了默认响应
                if (openaiResponse.choices.length === 1 &&
                    openaiResponse.choices[0].message.content === "I apologize, but I couldn't generate a response.") {
                    console.warn('⚠️ Using default response - Google AI returned empty or invalid candidates')
                }

                return new Response(JSON.stringify(openaiResponse), {
                    status: response.status,
                    headers: { 'Content-Type': 'application/json' }
                })
            } catch (responseError) {
                console.error('Failed to process response:', responseError)
                return new Response('Failed to process response', { status: 500 })
            }
        }

        // 6. 如果失败，直接返回原始响应
        console.warn('🔧 [COMPAT] Returning failed response with status:', response.status)
        return response
    } catch (error) {
        console.error('handleCompatGeminiDirect error:', error)
        return new Response('Internal server error in gemini direct', { status: 500 })
    }
}

/**
 * 🔧 OpenAI 兼容处理：compat + google-ai-studio 的真正兼容实现
 * 直接调用内部函数，不浪费 Cloudflare 调用次数
 */
async function handleCompatGoogleAIRedirect(
    request: Request,
    env: Env,
    ctx: ExecutionContext,
    model: string
): Promise<Response> {
    try {
        // 1. 解析 OpenAI 请求体，处理 JSON 解析错误
        let openaiBody: any
        try {
            openaiBody = await request.json()
        } catch (jsonError) {
            console.error('Invalid JSON in request body:', jsonError)
            return new Response('Invalid JSON in request body', { status: 400 })
        }

        // 2. 验证请求体格式
        if (!openaiBody || typeof openaiBody !== 'object') {
            return new Response('Request body must be a JSON object', { status: 400 })
        }

        if (!Array.isArray(openaiBody.messages) || openaiBody.messages.length === 0) {
            return new Response('Request body must contain a non-empty messages array', { status: 400 })
        }

        // 3. 转换为 Google AI Studio 格式
        let googleBody: any
        try {
            googleBody = convertOpenAIBodyToGoogleAI(openaiBody)
        } catch (conversionError) {
            console.error('Failed to convert OpenAI format to Google AI format:', conversionError)
            return new Response('Invalid request format', { status: 400 })
        }

        // 🔧 新增：检测是否需要流式响应
        if (openaiBody.stream) {
            return await handleStreamingResponse(googleBody, env, ctx, model, openaiBody)
        }

        // 4. 直接调用内部函数，不通过网络请求
        const response = await forwardToGoogleAI(googleBody, env, ctx, 'google-ai-studio', model)

        // 5. 如果成功，转换响应格式为 OpenAI 格式
        if (response.ok) {
            try {
                const googleResponseBody = await response.json()

                // 🔧 诊断日志：记录Google AI Studio的原始响应
                console.info('Google AI Studio response (compat redirect):', JSON.stringify(googleResponseBody, null, 2))

                const openaiResponse = convertGoogleAIBodyToOpenAI(googleResponseBody, model)

                // 🔧 诊断日志：检查是否使用了默认响应
                if (openaiResponse.choices.length === 1 &&
                    openaiResponse.choices[0].message.content === "I apologize, but I couldn't generate a response.") {
                    console.warn('⚠️ Using default response (compat redirect) - Google AI returned empty or invalid candidates')
                }

                return new Response(JSON.stringify(openaiResponse), {
                    status: response.status,
                    headers: { 'Content-Type': 'application/json' }
                })
            } catch (responseError) {
                console.error('Failed to process response:', responseError)
                return new Response('Failed to process response', { status: 500 })
            }
        }

        // 6. 如果失败，直接返回原始响应
        return response
    } catch (error) {
        console.error('handleCompatGoogleAIRedirect error:', error)
        return new Response('Internal server error in compat redirect', { status: 500 })
    }
}

/**
 * 转换 OpenAI 请求体到 Google AI Studio 格式（纯函数，不涉及网络请求）
 */
function convertOpenAIBodyToGoogleAI(openaiBody: any): any {
    // 验证输入
    if (!openaiBody || typeof openaiBody !== 'object') {
        throw new Error('Invalid openaiBody: must be an object')
    }

    if (!Array.isArray(openaiBody.messages)) {
        throw new Error('Invalid openaiBody: messages must be an array')
    }

    if (openaiBody.messages.length === 0) {
        throw new Error('Invalid openaiBody: messages array cannot be empty')
    }

    // 转换消息格式
    const contents = []
    for (const message of openaiBody.messages) {
        // 验证消息格式
        if (!message || typeof message !== 'object') {
            throw new Error('Invalid message: must be an object')
        }

        // 处理content字段：支持字符串和数组格式
        let contentText: string
        if (typeof message.content === 'string') {
            // 简单字符串格式
            contentText = message.content
        } else if (Array.isArray(message.content)) {
            // 数组格式（多模态消息）
            const textParts = message.content
                .filter((part: any) => part.type === 'text')
                .map((part: any) => part.text || '')

            if (textParts.length === 0) {
                throw new Error('Invalid message: content array must contain at least one text part')
            }

            contentText = textParts.join(' ')
        } else if (message.content === null && message.tool_calls) {
            // Cline支持：content为null但有tool_calls的情况
            contentText = ''
        } else {
            throw new Error('Invalid message: content must be a string, array, or null (with tool_calls)')
        }

        // 🔧 Cline扩展：支持更多角色类型
        const validRoles = ['user', 'assistant', 'system', 'tool', 'developer']
        if (!message.role || !validRoles.includes(message.role)) {
            throw new Error(`Invalid message: role must be one of ${validRoles.join(', ')}`)
        }

        // 🔧 Cline扩展：处理developer角色 (o1系列模型)
        let effectiveRole = message.role
        if (message.role === 'developer') {
            effectiveRole = 'user' // Google AI不支持developer角色，转换为user
        }

        // 对于单轮对话，使用简单格式（不包含 role）
        // 对于多轮对话，需要包含 role
        if (openaiBody.messages.length === 1 && contentText) {
            // 单轮对话：使用最简单的格式
            contents.push({
                parts: [{ text: contentText }]
            })
        } else if (contentText || message.role === 'tool') {
            // 多轮对话：包含 role 字段
            contents.push({
                role: effectiveRole === 'assistant' ? 'model' : 'user',
                parts: [{ text: contentText }]
            })
        }
    }

    // 构建 Google AI Studio 请求体
    const googleBody: any = {
        contents: contents
    }

    // 🔧 新增：启用思考模式 (Gemini 2.5系列支持)
    googleBody.generationConfig = googleBody.generationConfig || {}
    googleBody.generationConfig.thinkingConfig = {
        includeThoughts: true,  // 启用思考摘要
        thinkingBudget: -1      // 动态思考预算
    }

    // 🔧 Cline扩展：支持reasoning_effort参数
    if (openaiBody.reasoning_effort) {
        const effortMap = {
            'low': 512,
            'medium': 2048,
            'high': 8192
        }
        const budget = effortMap[openaiBody.reasoning_effort as keyof typeof effortMap]
        if (budget) {
            googleBody.generationConfig.thinkingConfig.thinkingBudget = budget
        }
    }

    // 转换可选参数，验证类型
    if (openaiBody.max_tokens !== undefined) {
        if (typeof openaiBody.max_tokens !== 'number' || openaiBody.max_tokens <= 0) {
            throw new Error('Invalid max_tokens: must be a positive number')
        }
        googleBody.generationConfig.maxOutputTokens = openaiBody.max_tokens
    }

    if (openaiBody.temperature !== undefined) {
        if (typeof openaiBody.temperature !== 'number' || openaiBody.temperature < 0 || openaiBody.temperature > 2) {
            throw new Error('Invalid temperature: must be a number between 0 and 2')
        }
        googleBody.generationConfig.temperature = openaiBody.temperature
    }

    if (openaiBody.top_p !== undefined) {
        if (typeof openaiBody.top_p !== 'number' || openaiBody.top_p < 0 || openaiBody.top_p > 1) {
            throw new Error('Invalid top_p: must be a number between 0 and 1')
        }
        googleBody.generationConfig.topP = openaiBody.top_p
    }

    return googleBody
}

/**
 * 转换 Google AI Studio 响应体到 OpenAI 格式（纯函数，不涉及网络请求）
 */
function convertGoogleAIBodyToOpenAI(googleBody: any, model: string): any {
    // 构建 OpenAI 格式的响应
    const openaiResponse: any = {
        id: `chatcmpl-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`,
        object: 'chat.completion',
        created: Math.floor(Date.now() / 1000),
        model: `google-ai-studio/${model}`,
        choices: [],
        usage: {
            prompt_tokens: 0,
            completion_tokens: 0,
            total_tokens: 0
        }
    }

    // 转换候选响应
    if (googleBody.candidates && googleBody.candidates.length > 0) {
        for (let i = 0; i < googleBody.candidates.length; i++) {
            const candidate = googleBody.candidates[i]
            const content = candidate.content

            if (content && content.parts && content.parts.length > 0) {
                const text = content.parts.map((part: any) => part.text || '').join('')

                openaiResponse.choices.push({
                    index: i,
                    message: {
                        role: 'assistant',
                        content: text
                    },
                    finish_reason: candidate.finishReason === 'STOP' ? 'stop' : 'length'
                })
            }
        }
    }

    // 如果没有有效的候选响应，创建一个默认响应
    if (openaiResponse.choices.length === 0) {
        // 🔧 诊断日志：记录触发默认响应的原因
        console.warn('⚠️ No valid candidates found in Google AI response, using default response')
        console.warn('Google AI response structure:', {
            hasCandidates: !!(googleBody.candidates),
            candidatesLength: googleBody.candidates?.length || 0,
            candidatesDetails: googleBody.candidates?.map((c: any, i: number) => ({
                index: i,
                hasContent: !!(c.content),
                hasParts: !!(c.content?.parts),
                partsLength: c.content?.parts?.length || 0,
                finishReason: c.finishReason
            })) || []
        })

        openaiResponse.choices.push({
            index: 0,
            message: {
                role: 'assistant',
                content: "I apologize, but I couldn't generate a response."
            },
            finish_reason: 'stop'
        })
    }

    // 尝试提取使用统计信息
    if (googleBody.usageMetadata) {
        openaiResponse.usage.prompt_tokens = googleBody.usageMetadata.promptTokenCount || 0
        openaiResponse.usage.completion_tokens = googleBody.usageMetadata.candidatesTokenCount || 0
        openaiResponse.usage.total_tokens =
            googleBody.usageMetadata.totalTokenCount ||
            openaiResponse.usage.prompt_tokens + openaiResponse.usage.completion_tokens
    }

    return openaiResponse
}

/**
 * 直接调用 Google AI Studio，不通过网络请求转发
 * 节省 Cloudflare 调用次数，复用现有的 forward 逻辑
 */
async function forwardToGoogleAI(
    googleBody: any,
    env: Env,
    ctx: ExecutionContext,
    provider: string,
    model: string
): Promise<Response> {
    try {
        // 验证输入参数
        if (!googleBody || typeof googleBody !== 'object') {
            return new Response('Invalid request body', { status: 400 })
        }

        // 获取可用的 keys
        const activeKeys = await keyService.listActiveKeysViaCache(env)
        if (activeKeys.length === 0) {
            return new Response('No active keys available', { status: 503 })
        }

        // 按 provider 过滤 keys
        const providerKeys = activeKeys.filter(key => key.provider === provider)
        if (providerKeys.length === 0) {
            return new Response(`No active keys available for provider ${provider}`, { status: 503 })
        }

        console.info(`Found ${providerKeys.length} active keys for provider ${provider} (total: ${activeKeys.length})`)

        // 序列化请求体，处理可能的错误
        let body: ArrayBuffer
        try {
            body = new TextEncoder().encode(JSON.stringify(googleBody))
        } catch (serializationError) {
            console.error('Failed to serialize request body:', serializationError)
            return new Response('Failed to serialize request body', { status: 400 })
        }

        const triedKeys = new Set<string>()
        const blockedKeys = new Set<string>()
        const MAX_RETRIES = Math.min(25, providerKeys.length * 2)

        for (let i = 0; i < MAX_RETRIES; i++) {
            const availableKeys = providerKeys.filter(key => !blockedKeys.has(key.id))
            if (availableKeys.length === 0) {
                return new Response(`No active ${provider} keys available after filtering`, { status: 503 })
            }

            let selectedKey: schema.Key
            try {
                selectedKey = await selectKey(availableKeys, model, triedKeys)
            } catch (error) {
                console.error('Failed to select key:', error)
                return new Response('Key selection failed', { status: 503 })
            }

            triedKeys.add(selectedKey.id)

            const restResource = `google-ai-studio/v1beta/models/${model}:generateContent`
            const headers = new Headers()
            headers.set('Content-Type', 'application/json')

            let reqToGateway: Request
            try {
                reqToGateway = await makeGatewayRequest(
                    'POST',
                    headers,
                    body,
                    env,
                    restResource,
                    selectedKey.key,
                    provider
                )
            } catch (gatewayError) {
                console.error('Failed to create gateway request:', gatewayError)
                continue // 尝试下一个 key
            }

            console.info(
                `selected concurrency-aware key ${selectedKey.id.substring(0, 8)}... (${availableKeys.length - 1} available)`
            )

            let response: Response
            try {
                response = await fetch(reqToGateway)
            } catch (fetchError) {
                console.error(`Network error for key ${selectedKey.id.substring(0, 8)}...:`, fetchError)
                continue // 尝试下一个 key
            }

            const status = response.status
            console.info(`attempt ${i + 1}/${MAX_RETRIES}: key ${selectedKey.id.substring(0, 8)}... returned ${status}`)

            // 🔧 使用与原有系统完全相同的错误处理逻辑
            switch (status) {
                case 400:
                    if (!(await keyIsInvalid(response, provider))) {
                        return response // ✅ 确认是用户错误，直接返回
                    }
                    // ✅ 400确认为key失效，执行key失效处理
                    console.error(`key ${selectedKey.id.substring(0, 8)}... invalid (400 analysis)`)

                    // ✅ 原子操作：先标记再删除，避免竞态条件
                    blockedKeys.add(selectedKey.id)
                    removeKeyFromArray(providerKeys, selectedKey)

                    // ✅ 异步持久化，不阻塞主流程
                    await updateKeyStatusAsync(ctx, env, selectedKey.id, 'blocked')
                    continue

                case 401:
                case 403:
                    console.error(`key ${selectedKey.id.substring(0, 8)}... blocked due to ${status}`)

                    // ✅ 原子操作：状态强一致性保证
                    blockedKeys.add(selectedKey.id)
                    removeKeyFromArray(providerKeys, selectedKey)

                    // ✅ 异步持久化数据库状态
                    await updateKeyStatusAsync(ctx, env, selectedKey.id, 'blocked')
                    continue

                case 429:
                    console.warn(`key ${selectedKey.id.substring(0, 8)}... rate limited for model ${model}`)
                    const cooldownSeconds = await analyze429CooldownSeconds(response, provider)

                    // ✅ 智能冷却：只从当前请求的可用池中移除，不影响其他模型
                    removeKeyFromArray(providerKeys, selectedKey)

                    // ✅ 异步更新模型级冷却，保持高性能
                    await updateKeyModelCooldownAsync(ctx, env, selectedKey.id, model, cooldownSeconds)
                    continue

                case 500:
                case 502:
                case 503:
                case 504:
                    // ✅ 服务端错误，不是key问题，继续尝试其他key
                    console.warn(
                        `provider error ${status} for key ${selectedKey.id.substring(0, 8)}..., trying next key`
                    )
                    continue

                default:
                    // ✅ 200或其他状态码，认为请求成功
                    return response
            }
        }

        // ✅ 详细的失败信息，便于调试（与原有系统一致）
        const finalMessage = `Internal server error after ${MAX_RETRIES} retries. Tried ${triedKeys.size} keys, blocked ${blockedKeys.size} keys.`
        console.error(finalMessage)
        return new Response(finalMessage, { status: 500 })
    } catch (error) {
        console.error('forwardToGoogleAI error:', error)
        return new Response('Internal server error', { status: 500 })
    }
}

/**
 * 🔧 流式响应处理：将Google AI流式响应转换为OpenAI兼容的SSE格式
 * 支持Roo-Code和Cline的所有要求
 */
async function handleStreamingResponse(
    googleBody: any,
    env: Env,
    ctx: ExecutionContext,
    model: string,
    originalRequest: any
): Promise<Response> {
    try {
        // 调用Google AI流式API
        const streamResponse = await forwardToGoogleAIStream(googleBody, env, ctx, 'google-ai-studio', model)

        if (!streamResponse.ok) {
            return streamResponse // 直接返回错误响应
        }

        // 创建转换流
        const { readable, writable } = new TransformStream()
        const writer = writable.getWriter()

        // 异步处理流转换
        processGoogleStreamToOpenAI(streamResponse, writer, model, originalRequest).catch(error => {
            console.error('Stream processing error:', error)
            writer.close()
        })

        return new Response(readable, {
            headers: {
                'Content-Type': 'text/event-stream',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': '*'
            }
        })
    } catch (error) {
        console.error('handleStreamingResponse error:', error)
        return new Response('Internal server error in streaming', { status: 500 })
    }
}

/**
 * 🔧 Google AI流式API调用：复用现有的forwardToGoogleAI逻辑，但调用流式端点
 */
async function forwardToGoogleAIStream(
    googleBody: any,
    env: Env,
    ctx: ExecutionContext,
    provider: string,
    model: string
): Promise<Response> {
    try {
        // 获取可用的 keys
        const activeKeys = await keyService.listActiveKeysViaCache(env)
        if (activeKeys.length === 0) {
            return new Response('No active keys available', { status: 503 })
        }

        // 按 provider 过滤 keys
        const providerKeys = activeKeys.filter(key => key.provider === provider)
        if (providerKeys.length === 0) {
            return new Response(`No active keys available for provider ${provider}`, { status: 503 })
        }

        console.info(`Found ${providerKeys.length} active keys for provider ${provider} (total: ${activeKeys.length})`)

        // 序列化请求体
        let body: ArrayBuffer
        try {
            body = new TextEncoder().encode(JSON.stringify(googleBody))
        } catch (serializationError) {
            console.error('Failed to serialize request body:', serializationError)
            return new Response('Failed to serialize request body', { status: 400 })
        }

        const triedKeys = new Set<string>()
        const MAX_RETRIES = Math.min(25, providerKeys.length * 2)

        for (let i = 0; i < MAX_RETRIES; i++) {
            const availableKeys = providerKeys.filter(key => !triedKeys.has(key.id))
            if (availableKeys.length === 0) {
                break
            }

            let selectedKey: schema.Key
            try {
                selectedKey = await selectKey(availableKeys, model, triedKeys)
            } catch (error) {
                console.error('Failed to select key:', error)
                return new Response('Key selection failed', { status: 503 })
            }

            triedKeys.add(selectedKey.id)

            // 🔧 关键：使用流式端点
            const restResource = `google-ai-studio/v1beta/models/${model}:streamGenerateContent?alt=sse`
            const headers = new Headers()
            headers.set('Content-Type', 'application/json')

            let reqToGateway: Request
            try {
                reqToGateway = await makeGatewayRequest(
                    'POST',
                    headers,
                    body,
                    env,
                    restResource,
                    selectedKey.key,
                    provider
                )
            } catch (gatewayError) {
                console.error('Failed to create gateway request:', gatewayError)
                continue
            }

            console.info(
                `selected concurrency-aware key ${selectedKey.id.substring(0, 8)}... (${availableKeys.length - 1} available)`
            )

            const response = await fetch(reqToGateway)
            const status = response.status
            console.info(`attempt ${i + 1}/${MAX_RETRIES}: key ${selectedKey.id.substring(0, 8)}... returned ${status}`)

            switch (status) {
                case 400:
                    if (!(await keyIsInvalid(response, provider))) {
                        return response // 用户错误
                    }
                    // key无效，继续下一个
                    await updateKeyStatusAsync(ctx, env, selectedKey.id, 'blocked')
                    continue

                case 401:
                case 403:
                    await updateKeyStatusAsync(ctx, env, selectedKey.id, 'blocked')
                    continue

                case 429:
                    console.warn(`key ${selectedKey.id.substring(0, 8)}... rate limited for model ${model}`)
                    const cooldownSeconds = await analyze429CooldownSeconds(response, provider)
                    await updateKeyModelCooldownAsync(ctx, env, selectedKey.id, model, cooldownSeconds)
                    continue

                case 500:
                case 502:
                case 503:
                case 504:
                    // ✅ 服务端错误，不是key问题，继续尝试其他key
                    console.warn(
                        `provider error ${status} for key ${selectedKey.id.substring(0, 8)}..., trying next key`
                    )
                    continue

                default:
                    return response
            }
        }

        // ✅ 详细的失败信息，便于调试（与原有系统一致）
        const finalMessage = `Internal server error after ${MAX_RETRIES} retries. Tried ${triedKeys.size} keys, blocked 0 keys.`
        console.error(finalMessage)
        return new Response(finalMessage, { status: 500 })
    } catch (error) {
        console.error('forwardToGoogleAIStream error:', error)
        return new Response('Internal server error', { status: 500 })
    }
}

/**
 * 🔧 核心流处理函数：将Google AI SSE流转换为OpenAI兼容格式
 * 完美支持Roo-Code和Cline的所有要求
 */
async function processGoogleStreamToOpenAI(
    googleStream: Response,
    writer: WritableStreamDefaultWriter<Uint8Array>,
    model: string,
    originalRequest: any
): Promise<void> {
    const reader = googleStream.body?.getReader()
    if (!reader) {
        await writer.close()
        return
    }

    const chatId = `chatcmpl-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`
    const timestamp = Math.floor(Date.now() / 1000)
    let totalInputTokens = 0
    let totalOutputTokens = 0
    let hasStarted = false
    // 🔧 [FIX 1/4] 引入状态标志，用于追踪是否已成功发送过任何有效内容。
    let hasSentContent = false;

    try {
        const decoder = new TextDecoder()
        let buffer = ''

        while (true) {
            const { done, value } = await reader.read()
            if (done) break

            buffer += decoder.decode(value, { stream: true })
            const lines = buffer.split('\n')
            buffer = lines.pop() || '' // 保留最后一个不完整的行

            for (const line of lines) {
                if (!line.trim()) continue

                if (line.startsWith('data: ')) {
                    const jsonStr = line.substring(6).trim()
                    if (jsonStr === '[DONE]') {
                        // 🔧 忽略原始的 [DONE] 信号，我们将在 finally 中统一发送。
                        continue
                    }

                    try {
                        const googleChunk = JSON.parse(jsonStr)

                        // 提取usage信息
                        if (googleChunk.usageMetadata) {
                            totalInputTokens = googleChunk.usageMetadata.promptTokenCount || 0
                            totalOutputTokens = googleChunk.usageMetadata.candidatesTokenCount || 0
                        }

                        // 转换为OpenAI格式
                        for (const candidate of googleChunk.candidates || []) {
                            if (!candidate.content?.parts) continue

                            for (const part of candidate.content.parts) {
                                if (!part.text) continue

                                // 🔧 发送角色信息（仅第一次）
                                if (!hasStarted) {
                                    const roleChunk = {
                                        id: chatId,
                                        object: 'chat.completion.chunk',
                                        created: timestamp,
                                        model: model, // 🔧 修复：使用原始model名称
                                        choices: [{
                                            index: 0,
                                            delta: { role: 'assistant' },
                                            finish_reason: null
                                        }]
                                    }
                                    const roleData = `data: ${JSON.stringify(roleChunk)}\n\n`
                                    await writer.write(new TextEncoder().encode(roleData))
                                    hasStarted = true
                                }

                                // 🔧 [FIX 2/4] 只要我们准备发送第一个有效的文本块，就将标志设为 true。
                                hasSentContent = true;

                                // 🔧 核心：区分思考内容和普通内容
                                const openaiChunk = {
                                    id: chatId,
                                    object: 'chat.completion.chunk',
                                    created: timestamp,
                                    model: model, // 🔧 修复：使用原始model名称
                                    choices: [{
                                        index: 0,
                                        delta: part.thought ?
                                            { reasoning_content: part.text } :  // 🔧 Roo-Code/Cline: 推理内容
                                            { content: part.text },             // 🔧 Roo-Code/Cline: 普通内容
                                        finish_reason: mapGoogleFinishReason(candidate.finishReason) // 🔧 修复：正确映射finish_reason
                                    }]
                                }

                                const sseData = `data: ${JSON.stringify(openaiChunk)}\n\n`
                                await writer.write(new TextEncoder().encode(sseData))
                            }
                        }
                    } catch (parseError) {
                        console.error('Failed to parse Google AI chunk:', parseError, 'Raw data:', jsonStr)
                        // 继续处理，不中断流
                    }
                }
            }
        }
    } catch (error) {
        console.error('Stream processing error:', error)
    } finally {
        // 🔧 [FIX 3/4] 最终检查点：在流完全结束后，但在关闭连接前。
        // 如果 hasSentContent 仍然是 false，说明整个流都是空的。
        if (!hasSentContent) {
            console.warn("⚠️ Google AI stream ended without any valid content. Sending a fallback error message to cline.");
            const errorChunk = {
                id: chatId,
                object: 'chat.completion.chunk',
                created: timestamp,
                model: model,
                choices: [{
                    index: 0,
                    // 发送一个对 cline 友好的错误消息
                    delta: { content: "[SERVER_ERROR] The language model returned an empty but successful response. This has been handled by the server to prevent a client-side freeze. Please try your request again." },
                    finish_reason: 'stop'
                }]
            };
            const errorData = `data: ${JSON.stringify(errorChunk)}\n\n`;
            await writer.write(new TextEncoder().encode(errorData));
        }

        // 🔧 [FIX 4/4] 统一在最后发送 usage (如果需要) 和 [DONE] 信号，确保连接总是被正确关闭。
        if (originalRequest.stream_options?.include_usage && (totalInputTokens > 0 || totalOutputTokens > 0)) {
            const usageChunk = {
                id: chatId,
                object: 'chat.completion.chunk',
                created: timestamp,
                model: model,
                choices: [],
                usage: {
                    prompt_tokens: totalInputTokens,
                    completion_tokens: totalOutputTokens,
                    total_tokens: totalInputTokens + totalOutputTokens
                }
            };
            const usageData = `data: ${JSON.stringify(usageChunk)}\n\n`;
            await writer.write(new TextEncoder().encode(usageData));
        }

        await writer.write(new TextEncoder().encode('data: [DONE]\n\n'));

        reader.releaseLock()
        await writer.close()
    }
}

/**
 * 🔧 Google AI finish_reason映射到OpenAI格式
 * 确保完全符合Cline和Roo-Code的要求
 */
function mapGoogleFinishReason(googleReason: string | undefined): "stop" | "length" | "tool_calls" | "content_filter" | null {
    if (!googleReason) return null

    switch (googleReason) {
        case 'STOP':
            return 'stop'
        case 'MAX_TOKENS':
            return 'length'
        case 'SAFETY':
            return 'content_filter'
        case 'RECITATION':
            return 'content_filter'
        case 'OTHER':
            return null
        default:
            return null
    }
}
