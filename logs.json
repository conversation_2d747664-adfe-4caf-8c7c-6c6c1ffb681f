
wrangler tail [worker]

🦚 Start a log tailing session for a Worker

POSITIONALS
  worker  Name or route of the worker to tail  [string]

GLOBAL FLAGS
  -c, --config   Path to Wrangler configuration file  [string]
      --cwd      Run as if Wrangler was started in the specified directory instead of the current working directory  [string]
  -e, --env      Environment to use for operations, and for selecting .env and .dev.vars files  [string]
  -h, --help     Show help  [boolean]
  -v, --version  Show version number  [boolean]

OPTIONS
      --format         The format of log entries  [choices: "json", "pretty"]
      --status         Filter by invocation status  [array] [choices: "ok", "error", "canceled"]
      --header         Filter by HTTP header  [string]
      --method         Filter by HTTP method  [array]
      --sampling-rate  Adds a percentage of requests to log sampling rate  [number]
      --search         Filter by a text match in console.log messages  [string]
      --ip             Filter by the IP address the request originates from. Use "self" to filter for your own IP  [array]
      --version-id     Filter by Worker version  [string]
