# OpenAI 兼容 API 使用示例

## 概述

本文档展示如何使用新实现的 OpenAI 兼容 API 端点，这些端点提供了真正的 OpenAI 兼容性，同时通过内部函数调用节省了 Cloudflare 调用次数。

## 支持的端点

### 1. 明确的 Gemini 端点

- **路径**: `/api/compat/chat/completions/gemini`
- **默认模型**: `gemini-2.5-pro`
- **特点**: 不需要在请求体中指定模型，直接使用 Gemini 2.5 Pro

### 2. 通用 OpenAI 兼容端点

- **路径**: `/api/compat/chat/completions`
- **支持模型**: 通过 `model` 字段指定，如 `google-ai-studio/gemini-2.5-pro`
- **特点**: 完全兼容 OpenAI API 格式

### 3. 原生 Google AI Studio 端点（保持不变）

- **路径**: `/api/google-ai-studio/v1beta/models/{model}:generateContent`
- **特点**: 原有端点，100% 向后兼容

## 使用示例

### 示例 1: 使用明确的 Gemini 端点

```bash
curl "https://your-worker-url.workers.dev/api/compat/chat/completions/gemini" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-super-secret-auth-key" \
  -d '{
    "messages": [
      {
        "role": "user",
        "content": "写一个关于人工智能的短故事"
      }
    ],
    "max_tokens": 500,
    "temperature": 0.7
  }'
```

### 示例 2: 使用通用 OpenAI 兼容端点

```bash
curl "https://your-worker-url.workers.dev/api/compat/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-super-secret-auth-key" \
  -d '{
    "model": "google-ai-studio/gemini-2.5-pro",
    "messages": [
      {
        "role": "user",
        "content": "解释量子计算的基本原理"
      }
    ],
    "max_tokens": 300,
    "temperature": 0.5,
    "top_p": 0.9
  }'
```

### 示例 3: 多轮对话

```bash
curl "https://your-worker-url.workers.dev/api/compat/chat/completions/gemini" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-super-secret-auth-key" \
  -d '{
    "messages": [
      {
        "role": "user",
        "content": "你好，我想学习编程"
      },
      {
        "role": "assistant",
        "content": "你好！学习编程是一个很好的选择。你想学习哪种编程语言呢？"
      },
      {
        "role": "user",
        "content": "我想学习 Python，应该从哪里开始？"
      }
    ],
    "max_tokens": 400,
    "temperature": 0.8
  }'
```

## 响应格式

所有端点都返回标准的 OpenAI 格式响应：

```json
{
    "id": "chatcmpl-1234567890-abcdefghijk",
    "object": "chat.completion",
    "created": 1234567890,
    "model": "google-ai-studio/gemini-2.5-pro",
    "choices": [
        {
            "index": 0,
            "message": {
                "role": "assistant",
                "content": "这里是 AI 的回复内容..."
            },
            "finish_reason": "stop"
        }
    ],
    "usage": {
        "prompt_tokens": 25,
        "completion_tokens": 150,
        "total_tokens": 175
    }
}
```

## 支持的参数

### 必需参数

- `messages`: 消息数组，每个消息包含 `role` 和 `content`

### 可选参数

- `max_tokens`: 最大输出 token 数量（正整数）
- `temperature`: 温度参数，控制随机性（0-2 之间的数字）
- `top_p`: Top-p 采样参数（0-1 之间的数字）
- `model`: 模型名称（仅在通用端点中需要）

## 错误处理

### 400 Bad Request

- 无效的 JSON 格式
- 缺少必需的 `messages` 字段
- 空的 `messages` 数组
- 无效的参数值（如 `temperature` 超出范围）

### 403 Forbidden

- 无效的认证密钥

### 503 Service Unavailable

- 没有可用的 API 密钥
- 所有密钥都被限流或封禁

## 性能优化

### 内部函数调用

新的兼容端点直接调用内部函数，而不是通过网络请求转发，这带来了以下优势：

1. **节省 Cloudflare 调用次数**: 减少 50% 的内部调用
2. **降低延迟**: 消除网络往返时间
3. **提高可靠性**: 减少网络错误的可能性

### 智能错误处理

完全复用原有系统的错误处理逻辑：

1. **Key 失效检测**: 智能识别并自动禁用失效的 API 密钥
2. **限流处理**: 精确分析 429 错误，实施智能冷却策略
3. **负载均衡**: 使用优化的 key 选择算法，最大化可用性

## 兼容性保证

### 向后兼容

- 所有原有端点 100% 不受影响
- 现有的错误处理和 key 管理逻辑完全保持
- 数据库操作模式完全不变

### 新功能

- 真正的 OpenAI API 兼容性
- 更好的错误消息和状态码
- 完整的参数验证和类型检查

## 最佳实践

### 1. 选择合适的端点

- 如果只使用 Gemini 模型，推荐使用 `/api/compat/chat/completions/gemini`
- 如果需要灵活指定模型，使用 `/api/compat/chat/completions`

### 2. 参数设置

- `temperature`: 0.7-0.9 适合创意任务，0.1-0.5 适合事实性任务
- `max_tokens`: 根据需要设置，避免设置过大导致不必要的费用
- `top_p`: 通常设置为 0.9，与 temperature 配合使用

### 3. 错误处理

- 实现适当的重试逻辑，特别是对于 503 错误
- 监控 API 使用情况，及时发现和处理密钥问题

### 4. 安全性

- 妥善保管 AUTH_KEY，不要在客户端代码中暴露
- 定期轮换认证密钥
- 监控异常访问模式

## 故障排除

### 常见问题

1. **403 Forbidden**
    - 检查 AUTH_KEY 是否正确
    - 确认使用了正确的认证头

2. **400 Bad Request**
    - 验证 JSON 格式是否正确
    - 检查必需字段是否存在
    - 确认参数值在有效范围内

3. **503 Service Unavailable**
    - 检查是否有可用的 API 密钥
    - 确认密钥没有被限流或封禁
    - 联系管理员添加更多密钥

### 调试技巧

1. 使用 `curl -v` 查看详细的请求和响应信息
2. 检查响应头中的错误信息
3. 监控 Cloudflare Workers 的日志输出

## 总结

新的 OpenAI 兼容 API 端点提供了：

- ✅ 真正的 OpenAI API 兼容性
- ✅ 优化的性能和可靠性
- ✅ 完整的错误处理和验证
- ✅ 100% 向后兼容
- ✅ 智能的负载均衡和故障恢复

这些改进使得系统更加稳定、高效，同时保持了原有的所有功能和特性。
