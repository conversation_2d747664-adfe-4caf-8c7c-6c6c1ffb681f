lockfileVersion: '9.0'

settings:
    autoInstallPeers: true
    excludeLinksFromLockfile: false

importers:
    .:
        dependencies:
            drizzle-orm:
                specifier: ^0.44.2
                version: 0.44.2
        devDependencies:
            drizzle-kit:
                specifier: ^0.31.2
                version: 0.31.4
            prettier:
                specifier: ^3.6.2
                version: 3.6.2
            typescript:
                specifier: ^5.5.2
                version: 5.8.3
            wrangler:
                specifier: ^4.21.0
                version: 4.24.1

packages:
    '@cloudflare/kv-asset-handler@0.4.0':
        resolution:
            {
                integrity: sha512-+tv3z+SPp+gqTIcImN9o0hqE9xyfQjI1XD9pL6NuKjua9B1y7mNYv0S9cP+QEbA4ppVgGZEmKOvHX5G5Ei1CVA==
            }
        engines: { node: '>=18.0.0' }

    '@cloudflare/unenv-preset@2.3.3':
        resolution:
            {
                integrity: sha512-/M3MEcj3V2WHIRSW1eAQBPRJ6JnGQHc6JKMAPLkDb7pLs3m6X9ES/+K3ceGqxI6TKeF32AWAi7ls0AYzVxCP0A==
            }
        peerDependencies:
            unenv: 2.0.0-rc.17
            workerd: ^1.20250508.0
        peerDependenciesMeta:
            workerd:
                optional: true

    '@cloudflare/workerd-darwin-64@1.20250709.0':
        resolution:
            {
                integrity: sha512-VqwcvnbI8FNCP87ZWNHA3/sAC5U9wMbNnjBG0sHEYzM7B9RPHKYHdVKdBEWhzZXnkQYMK81IHm4CZsK16XxAuQ==
            }
        engines: { node: '>=16' }
        cpu: [x64]
        os: [darwin]

    '@cloudflare/workerd-darwin-arm64@1.20250709.0':
        resolution:
            {
                integrity: sha512-A54ttSgXMM4huChPTThhkieOjpDxR+srVOO9zjTHVIyoQxA8zVsku4CcY/GQ95RczMV+yCKVVu/tAME7vwBFuA==
            }
        engines: { node: '>=16' }
        cpu: [arm64]
        os: [darwin]

    '@cloudflare/workerd-linux-64@1.20250709.0':
        resolution:
            {
                integrity: sha512-no4O3OK+VXINIxv99OHJDpIgML2ZssrSvImwLtULzqm+cl4t1PIfXNRUqj89ujTkmad+L9y4G6dBQMPCLnmlGg==
            }
        engines: { node: '>=16' }
        cpu: [x64]
        os: [linux]

    '@cloudflare/workerd-linux-arm64@1.20250709.0':
        resolution:
            {
                integrity: sha512-7cNICk2Qd+m4QGrcmWyAuZJXTHt1ud6isA+dic7Yk42WZmwXhlcUATyvFD9FSQNFcldjuRB4n8JlWEFqZBn+lw==
            }
        engines: { node: '>=16' }
        cpu: [arm64]
        os: [linux]

    '@cloudflare/workerd-windows-64@1.20250709.0':
        resolution:
            {
                integrity: sha512-j1AyO8V/62Q23EJplWgzBlRCqo/diXgox58AbDqSqgyzCBAlvUzXQRDBab/FPNG/erRqt7I1zQhahrBhrM0uLA==
            }
        engines: { node: '>=16' }
        cpu: [x64]
        os: [win32]

    '@cspotcode/source-map-support@0.8.1':
        resolution:
            {
                integrity: sha512-IchNf6dN4tHoMFIn/7OE8LWZ19Y6q/67Bmf6vnGREv8RSbBVb9LPJxEcnwrcwX6ixSvaiGoomAUvu4YSxXrVgw==
            }
        engines: { node: '>=12' }

    '@drizzle-team/brocli@0.10.2':
        resolution:
            {
                integrity: sha512-z33Il7l5dKjUgGULTqBsQBQwckHh5AbIuxhdsIxDDiZAzBOrZO6q9ogcWC65kU382AfynTfgNumVcNIjuIua6w==
            }

    '@emnapi/runtime@1.4.4':
        resolution:
            {
                integrity: sha512-hHyapA4A3gPaDCNfiqyZUStTMqIkKRshqPIuDOXv1hcBnD4U3l8cP0T1HMCfGRxQ6V64TGCcoswChANyOAwbQg==
            }

    '@esbuild-kit/core-utils@3.3.2':
        resolution:
            {
                integrity: sha512-sPRAnw9CdSsRmEtnsl2WXWdyquogVpB3yZ3dgwJfe8zrOzTsV7cJvmwrKVa+0ma5BoiGJ+BoqkMvawbayKUsqQ==
            }
        deprecated: 'Merged into tsx: https://tsx.is'

    '@esbuild-kit/esm-loader@2.6.5':
        resolution:
            {
                integrity: sha512-FxEMIkJKnodyA1OaCUoEvbYRkoZlLZ4d/eXFu9Fh8CbBBgP5EmZxrfTRyN0qpXZ4vOvqnE5YdRdcrmUUXuU+dA==
            }
        deprecated: 'Merged into tsx: https://tsx.is'

    '@esbuild/aix-ppc64@0.25.4':
        resolution:
            {
                integrity: sha512-1VCICWypeQKhVbE9oW/sJaAmjLxhVqacdkvPLEjwlttjfwENRSClS8EjBz0KzRyFSCPDIkuXW34Je/vk7zdB7Q==
            }
        engines: { node: '>=18' }
        cpu: [ppc64]
        os: [aix]

    '@esbuild/aix-ppc64@0.25.6':
        resolution:
            {
                integrity: sha512-ShbM/3XxwuxjFiuVBHA+d3j5dyac0aEVVq1oluIDf71hUw0aRF59dV/efUsIwFnR6m8JNM2FjZOzmaZ8yG61kw==
            }
        engines: { node: '>=18' }
        cpu: [ppc64]
        os: [aix]

    '@esbuild/android-arm64@0.18.20':
        resolution:
            {
                integrity: sha512-Nz4rJcchGDtENV0eMKUNa6L12zz2zBDXuhj/Vjh18zGqB44Bi7MBMSXjgunJgjRhCmKOjnPuZp4Mb6OKqtMHLQ==
            }
        engines: { node: '>=12' }
        cpu: [arm64]
        os: [android]

    '@esbuild/android-arm64@0.25.4':
        resolution:
            {
                integrity: sha512-bBy69pgfhMGtCnwpC/x5QhfxAz/cBgQ9enbtwjf6V9lnPI/hMyT9iWpR1arm0l3kttTr4L0KSLpKmLp/ilKS9A==
            }
        engines: { node: '>=18' }
        cpu: [arm64]
        os: [android]

    '@esbuild/android-arm64@0.25.6':
        resolution:
            {
                integrity: sha512-hd5zdUarsK6strW+3Wxi5qWws+rJhCCbMiC9QZyzoxfk5uHRIE8T287giQxzVpEvCwuJ9Qjg6bEjcRJcgfLqoA==
            }
        engines: { node: '>=18' }
        cpu: [arm64]
        os: [android]

    '@esbuild/android-arm@0.18.20':
        resolution:
            {
                integrity: sha512-fyi7TDI/ijKKNZTUJAQqiG5T7YjJXgnzkURqmGj13C6dCqckZBLdl4h7bkhHt/t0WP+zO9/zwroDvANaOqO5Sw==
            }
        engines: { node: '>=12' }
        cpu: [arm]
        os: [android]

    '@esbuild/android-arm@0.25.4':
        resolution:
            {
                integrity: sha512-QNdQEps7DfFwE3hXiU4BZeOV68HHzYwGd0Nthhd3uCkkEKK7/R6MTgM0P7H7FAs5pU/DIWsviMmEGxEoxIZ+ZQ==
            }
        engines: { node: '>=18' }
        cpu: [arm]
        os: [android]

    '@esbuild/android-arm@0.25.6':
        resolution:
            {
                integrity: sha512-S8ToEOVfg++AU/bHwdksHNnyLyVM+eMVAOf6yRKFitnwnbwwPNqKr3srzFRe7nzV69RQKb5DgchIX5pt3L53xg==
            }
        engines: { node: '>=18' }
        cpu: [arm]
        os: [android]

    '@esbuild/android-x64@0.18.20':
        resolution:
            {
                integrity: sha512-8GDdlePJA8D6zlZYJV/jnrRAi6rOiNaCC/JclcXpB+KIuvfBN4owLtgzY2bsxnx666XjJx2kDPUmnTtR8qKQUg==
            }
        engines: { node: '>=12' }
        cpu: [x64]
        os: [android]

    '@esbuild/android-x64@0.25.4':
        resolution:
            {
                integrity: sha512-TVhdVtQIFuVpIIR282btcGC2oGQoSfZfmBdTip2anCaVYcqWlZXGcdcKIUklfX2wj0JklNYgz39OBqh2cqXvcQ==
            }
        engines: { node: '>=18' }
        cpu: [x64]
        os: [android]

    '@esbuild/android-x64@0.25.6':
        resolution:
            {
                integrity: sha512-0Z7KpHSr3VBIO9A/1wcT3NTy7EB4oNC4upJ5ye3R7taCc2GUdeynSLArnon5G8scPwaU866d3H4BCrE5xLW25A==
            }
        engines: { node: '>=18' }
        cpu: [x64]
        os: [android]

    '@esbuild/darwin-arm64@0.18.20':
        resolution:
            {
                integrity: sha512-bxRHW5kHU38zS2lPTPOyuyTm+S+eobPUnTNkdJEfAddYgEcll4xkT8DB9d2008DtTbl7uJag2HuE5NZAZgnNEA==
            }
        engines: { node: '>=12' }
        cpu: [arm64]
        os: [darwin]

    '@esbuild/darwin-arm64@0.25.4':
        resolution:
            {
                integrity: sha512-Y1giCfM4nlHDWEfSckMzeWNdQS31BQGs9/rouw6Ub91tkK79aIMTH3q9xHvzH8d0wDru5Ci0kWB8b3up/nl16g==
            }
        engines: { node: '>=18' }
        cpu: [arm64]
        os: [darwin]

    '@esbuild/darwin-arm64@0.25.6':
        resolution:
            {
                integrity: sha512-FFCssz3XBavjxcFxKsGy2DYK5VSvJqa6y5HXljKzhRZ87LvEi13brPrf/wdyl/BbpbMKJNOr1Sd0jtW4Ge1pAA==
            }
        engines: { node: '>=18' }
        cpu: [arm64]
        os: [darwin]

    '@esbuild/darwin-x64@0.18.20':
        resolution:
            {
                integrity: sha512-pc5gxlMDxzm513qPGbCbDukOdsGtKhfxD1zJKXjCCcU7ju50O7MeAZ8c4krSJcOIJGFR+qx21yMMVYwiQvyTyQ==
            }
        engines: { node: '>=12' }
        cpu: [x64]
        os: [darwin]

    '@esbuild/darwin-x64@0.25.4':
        resolution:
            {
                integrity: sha512-CJsry8ZGM5VFVeyUYB3cdKpd/H69PYez4eJh1W/t38vzutdjEjtP7hB6eLKBoOdxcAlCtEYHzQ/PJ/oU9I4u0A==
            }
        engines: { node: '>=18' }
        cpu: [x64]
        os: [darwin]

    '@esbuild/darwin-x64@0.25.6':
        resolution:
            {
                integrity: sha512-GfXs5kry/TkGM2vKqK2oyiLFygJRqKVhawu3+DOCk7OxLy/6jYkWXhlHwOoTb0WqGnWGAS7sooxbZowy+pK9Yg==
            }
        engines: { node: '>=18' }
        cpu: [x64]
        os: [darwin]

    '@esbuild/freebsd-arm64@0.18.20':
        resolution:
            {
                integrity: sha512-yqDQHy4QHevpMAaxhhIwYPMv1NECwOvIpGCZkECn8w2WFHXjEwrBn3CeNIYsibZ/iZEUemj++M26W3cNR5h+Tw==
            }
        engines: { node: '>=12' }
        cpu: [arm64]
        os: [freebsd]

    '@esbuild/freebsd-arm64@0.25.4':
        resolution:
            {
                integrity: sha512-yYq+39NlTRzU2XmoPW4l5Ifpl9fqSk0nAJYM/V/WUGPEFfek1epLHJIkTQM6bBs1swApjO5nWgvr843g6TjxuQ==
            }
        engines: { node: '>=18' }
        cpu: [arm64]
        os: [freebsd]

    '@esbuild/freebsd-arm64@0.25.6':
        resolution:
            {
                integrity: sha512-aoLF2c3OvDn2XDTRvn8hN6DRzVVpDlj2B/F66clWd/FHLiHaG3aVZjxQX2DYphA5y/evbdGvC6Us13tvyt4pWg==
            }
        engines: { node: '>=18' }
        cpu: [arm64]
        os: [freebsd]

    '@esbuild/freebsd-x64@0.18.20':
        resolution:
            {
                integrity: sha512-tgWRPPuQsd3RmBZwarGVHZQvtzfEBOreNuxEMKFcd5DaDn2PbBxfwLcj4+aenoh7ctXcbXmOQIn8HI6mCSw5MQ==
            }
        engines: { node: '>=12' }
        cpu: [x64]
        os: [freebsd]

    '@esbuild/freebsd-x64@0.25.4':
        resolution:
            {
                integrity: sha512-0FgvOJ6UUMflsHSPLzdfDnnBBVoCDtBTVyn/MrWloUNvq/5SFmh13l3dvgRPkDihRxb77Y17MbqbCAa2strMQQ==
            }
        engines: { node: '>=18' }
        cpu: [x64]
        os: [freebsd]

    '@esbuild/freebsd-x64@0.25.6':
        resolution:
            {
                integrity: sha512-2SkqTjTSo2dYi/jzFbU9Plt1vk0+nNg8YC8rOXXea+iA3hfNJWebKYPs3xnOUf9+ZWhKAaxnQNUf2X9LOpeiMQ==
            }
        engines: { node: '>=18' }
        cpu: [x64]
        os: [freebsd]

    '@esbuild/linux-arm64@0.18.20':
        resolution:
            {
                integrity: sha512-2YbscF+UL7SQAVIpnWvYwM+3LskyDmPhe31pE7/aoTMFKKzIc9lLbyGUpmmb8a8AixOL61sQ/mFh3jEjHYFvdA==
            }
        engines: { node: '>=12' }
        cpu: [arm64]
        os: [linux]

    '@esbuild/linux-arm64@0.25.4':
        resolution:
            {
                integrity: sha512-+89UsQTfXdmjIvZS6nUnOOLoXnkUTB9hR5QAeLrQdzOSWZvNSAXAtcRDHWtqAUtAmv7ZM1WPOOeSxDzzzMogiQ==
            }
        engines: { node: '>=18' }
        cpu: [arm64]
        os: [linux]

    '@esbuild/linux-arm64@0.25.6':
        resolution:
            {
                integrity: sha512-b967hU0gqKd9Drsh/UuAm21Khpoh6mPBSgz8mKRq4P5mVK8bpA+hQzmm/ZwGVULSNBzKdZPQBRT3+WuVavcWsQ==
            }
        engines: { node: '>=18' }
        cpu: [arm64]
        os: [linux]

    '@esbuild/linux-arm@0.18.20':
        resolution:
            {
                integrity: sha512-/5bHkMWnq1EgKr1V+Ybz3s1hWXok7mDFUMQ4cG10AfW3wL02PSZi5kFpYKrptDsgb2WAJIvRcDm+qIvXf/apvg==
            }
        engines: { node: '>=12' }
        cpu: [arm]
        os: [linux]

    '@esbuild/linux-arm@0.25.4':
        resolution:
            {
                integrity: sha512-kro4c0P85GMfFYqW4TWOpvmF8rFShbWGnrLqlzp4X1TNWjRY3JMYUfDCtOxPKOIY8B0WC8HN51hGP4I4hz4AaQ==
            }
        engines: { node: '>=18' }
        cpu: [arm]
        os: [linux]

    '@esbuild/linux-arm@0.25.6':
        resolution:
            {
                integrity: sha512-SZHQlzvqv4Du5PrKE2faN0qlbsaW/3QQfUUc6yO2EjFcA83xnwm91UbEEVx4ApZ9Z5oG8Bxz4qPE+HFwtVcfyw==
            }
        engines: { node: '>=18' }
        cpu: [arm]
        os: [linux]

    '@esbuild/linux-ia32@0.18.20':
        resolution:
            {
                integrity: sha512-P4etWwq6IsReT0E1KHU40bOnzMHoH73aXp96Fs8TIT6z9Hu8G6+0SHSw9i2isWrD2nbx2qo5yUqACgdfVGx7TA==
            }
        engines: { node: '>=12' }
        cpu: [ia32]
        os: [linux]

    '@esbuild/linux-ia32@0.25.4':
        resolution:
            {
                integrity: sha512-yTEjoapy8UP3rv8dB0ip3AfMpRbyhSN3+hY8mo/i4QXFeDxmiYbEKp3ZRjBKcOP862Ua4b1PDfwlvbuwY7hIGQ==
            }
        engines: { node: '>=18' }
        cpu: [ia32]
        os: [linux]

    '@esbuild/linux-ia32@0.25.6':
        resolution:
            {
                integrity: sha512-aHWdQ2AAltRkLPOsKdi3xv0mZ8fUGPdlKEjIEhxCPm5yKEThcUjHpWB1idN74lfXGnZ5SULQSgtr5Qos5B0bPw==
            }
        engines: { node: '>=18' }
        cpu: [ia32]
        os: [linux]

    '@esbuild/linux-loong64@0.18.20':
        resolution:
            {
                integrity: sha512-nXW8nqBTrOpDLPgPY9uV+/1DjxoQ7DoB2N8eocyq8I9XuqJ7BiAMDMf9n1xZM9TgW0J8zrquIb/A7s3BJv7rjg==
            }
        engines: { node: '>=12' }
        cpu: [loong64]
        os: [linux]

    '@esbuild/linux-loong64@0.25.4':
        resolution:
            {
                integrity: sha512-NeqqYkrcGzFwi6CGRGNMOjWGGSYOpqwCjS9fvaUlX5s3zwOtn1qwg1s2iE2svBe4Q/YOG1q6875lcAoQK/F4VA==
            }
        engines: { node: '>=18' }
        cpu: [loong64]
        os: [linux]

    '@esbuild/linux-loong64@0.25.6':
        resolution:
            {
                integrity: sha512-VgKCsHdXRSQ7E1+QXGdRPlQ/e08bN6WMQb27/TMfV+vPjjTImuT9PmLXupRlC90S1JeNNW5lzkAEO/McKeJ2yg==
            }
        engines: { node: '>=18' }
        cpu: [loong64]
        os: [linux]

    '@esbuild/linux-mips64el@0.18.20':
        resolution:
            {
                integrity: sha512-d5NeaXZcHp8PzYy5VnXV3VSd2D328Zb+9dEq5HE6bw6+N86JVPExrA6O68OPwobntbNJ0pzCpUFZTo3w0GyetQ==
            }
        engines: { node: '>=12' }
        cpu: [mips64el]
        os: [linux]

    '@esbuild/linux-mips64el@0.25.4':
        resolution:
            {
                integrity: sha512-IcvTlF9dtLrfL/M8WgNI/qJYBENP3ekgsHbYUIzEzq5XJzzVEV/fXY9WFPfEEXmu3ck2qJP8LG/p3Q8f7Zc2Xg==
            }
        engines: { node: '>=18' }
        cpu: [mips64el]
        os: [linux]

    '@esbuild/linux-mips64el@0.25.6':
        resolution:
            {
                integrity: sha512-WViNlpivRKT9/py3kCmkHnn44GkGXVdXfdc4drNmRl15zVQ2+D2uFwdlGh6IuK5AAnGTo2qPB1Djppj+t78rzw==
            }
        engines: { node: '>=18' }
        cpu: [mips64el]
        os: [linux]

    '@esbuild/linux-ppc64@0.18.20':
        resolution:
            {
                integrity: sha512-WHPyeScRNcmANnLQkq6AfyXRFr5D6N2sKgkFo2FqguP44Nw2eyDlbTdZwd9GYk98DZG9QItIiTlFLHJHjxP3FA==
            }
        engines: { node: '>=12' }
        cpu: [ppc64]
        os: [linux]

    '@esbuild/linux-ppc64@0.25.4':
        resolution:
            {
                integrity: sha512-HOy0aLTJTVtoTeGZh4HSXaO6M95qu4k5lJcH4gxv56iaycfz1S8GO/5Jh6X4Y1YiI0h7cRyLi+HixMR+88swag==
            }
        engines: { node: '>=18' }
        cpu: [ppc64]
        os: [linux]

    '@esbuild/linux-ppc64@0.25.6':
        resolution:
            {
                integrity: sha512-wyYKZ9NTdmAMb5730I38lBqVu6cKl4ZfYXIs31Baf8aoOtB4xSGi3THmDYt4BTFHk7/EcVixkOV2uZfwU3Q2Jw==
            }
        engines: { node: '>=18' }
        cpu: [ppc64]
        os: [linux]

    '@esbuild/linux-riscv64@0.18.20':
        resolution:
            {
                integrity: sha512-WSxo6h5ecI5XH34KC7w5veNnKkju3zBRLEQNY7mv5mtBmrP/MjNBCAlsM2u5hDBlS3NGcTQpoBvRzqBcRtpq1A==
            }
        engines: { node: '>=12' }
        cpu: [riscv64]
        os: [linux]

    '@esbuild/linux-riscv64@0.25.4':
        resolution:
            {
                integrity: sha512-i8JUDAufpz9jOzo4yIShCTcXzS07vEgWzyX3NH2G7LEFVgrLEhjwL3ajFE4fZI3I4ZgiM7JH3GQ7ReObROvSUA==
            }
        engines: { node: '>=18' }
        cpu: [riscv64]
        os: [linux]

    '@esbuild/linux-riscv64@0.25.6':
        resolution:
            {
                integrity: sha512-KZh7bAGGcrinEj4qzilJ4hqTY3Dg2U82c8bv+e1xqNqZCrCyc+TL9AUEn5WGKDzm3CfC5RODE/qc96OcbIe33w==
            }
        engines: { node: '>=18' }
        cpu: [riscv64]
        os: [linux]

    '@esbuild/linux-s390x@0.18.20':
        resolution:
            {
                integrity: sha512-+8231GMs3mAEth6Ja1iK0a1sQ3ohfcpzpRLH8uuc5/KVDFneH6jtAJLFGafpzpMRO6DzJ6AvXKze9LfFMrIHVQ==
            }
        engines: { node: '>=12' }
        cpu: [s390x]
        os: [linux]

    '@esbuild/linux-s390x@0.25.4':
        resolution:
            {
                integrity: sha512-jFnu+6UbLlzIjPQpWCNh5QtrcNfMLjgIavnwPQAfoGx4q17ocOU9MsQ2QVvFxwQoWpZT8DvTLooTvmOQXkO51g==
            }
        engines: { node: '>=18' }
        cpu: [s390x]
        os: [linux]

    '@esbuild/linux-s390x@0.25.6':
        resolution:
            {
                integrity: sha512-9N1LsTwAuE9oj6lHMyyAM+ucxGiVnEqUdp4v7IaMmrwb06ZTEVCIs3oPPplVsnjPfyjmxwHxHMF8b6vzUVAUGw==
            }
        engines: { node: '>=18' }
        cpu: [s390x]
        os: [linux]

    '@esbuild/linux-x64@0.18.20':
        resolution:
            {
                integrity: sha512-UYqiqemphJcNsFEskc73jQ7B9jgwjWrSayxawS6UVFZGWrAAtkzjxSqnoclCXxWtfwLdzU+vTpcNYhpn43uP1w==
            }
        engines: { node: '>=12' }
        cpu: [x64]
        os: [linux]

    '@esbuild/linux-x64@0.25.4':
        resolution:
            {
                integrity: sha512-6e0cvXwzOnVWJHq+mskP8DNSrKBr1bULBvnFLpc1KY+d+irZSgZ02TGse5FsafKS5jg2e4pbvK6TPXaF/A6+CA==
            }
        engines: { node: '>=18' }
        cpu: [x64]
        os: [linux]

    '@esbuild/linux-x64@0.25.6':
        resolution:
            {
                integrity: sha512-A6bJB41b4lKFWRKNrWoP2LHsjVzNiaurf7wyj/XtFNTsnPuxwEBWHLty+ZE0dWBKuSK1fvKgrKaNjBS7qbFKig==
            }
        engines: { node: '>=18' }
        cpu: [x64]
        os: [linux]

    '@esbuild/netbsd-arm64@0.25.4':
        resolution:
            {
                integrity: sha512-vUnkBYxZW4hL/ie91hSqaSNjulOnYXE1VSLusnvHg2u3jewJBz3YzB9+oCw8DABeVqZGg94t9tyZFoHma8gWZQ==
            }
        engines: { node: '>=18' }
        cpu: [arm64]
        os: [netbsd]

    '@esbuild/netbsd-arm64@0.25.6':
        resolution:
            {
                integrity: sha512-IjA+DcwoVpjEvyxZddDqBY+uJ2Snc6duLpjmkXm/v4xuS3H+3FkLZlDm9ZsAbF9rsfP3zeA0/ArNDORZgrxR/Q==
            }
        engines: { node: '>=18' }
        cpu: [arm64]
        os: [netbsd]

    '@esbuild/netbsd-x64@0.18.20':
        resolution:
            {
                integrity: sha512-iO1c++VP6xUBUmltHZoMtCUdPlnPGdBom6IrO4gyKPFFVBKioIImVooR5I83nTew5UOYrk3gIJhbZh8X44y06A==
            }
        engines: { node: '>=12' }
        cpu: [x64]
        os: [netbsd]

    '@esbuild/netbsd-x64@0.25.4':
        resolution:
            {
                integrity: sha512-XAg8pIQn5CzhOB8odIcAm42QsOfa98SBeKUdo4xa8OvX8LbMZqEtgeWE9P/Wxt7MlG2QqvjGths+nq48TrUiKw==
            }
        engines: { node: '>=18' }
        cpu: [x64]
        os: [netbsd]

    '@esbuild/netbsd-x64@0.25.6':
        resolution:
            {
                integrity: sha512-dUXuZr5WenIDlMHdMkvDc1FAu4xdWixTCRgP7RQLBOkkGgwuuzaGSYcOpW4jFxzpzL1ejb8yF620UxAqnBrR9g==
            }
        engines: { node: '>=18' }
        cpu: [x64]
        os: [netbsd]

    '@esbuild/openbsd-arm64@0.25.4':
        resolution:
            {
                integrity: sha512-Ct2WcFEANlFDtp1nVAXSNBPDxyU+j7+tId//iHXU2f/lN5AmO4zLyhDcpR5Cz1r08mVxzt3Jpyt4PmXQ1O6+7A==
            }
        engines: { node: '>=18' }
        cpu: [arm64]
        os: [openbsd]

    '@esbuild/openbsd-arm64@0.25.6':
        resolution:
            {
                integrity: sha512-l8ZCvXP0tbTJ3iaqdNf3pjaOSd5ex/e6/omLIQCVBLmHTlfXW3zAxQ4fnDmPLOB1x9xrcSi/xtCWFwCZRIaEwg==
            }
        engines: { node: '>=18' }
        cpu: [arm64]
        os: [openbsd]

    '@esbuild/openbsd-x64@0.18.20':
        resolution:
            {
                integrity: sha512-e5e4YSsuQfX4cxcygw/UCPIEP6wbIL+se3sxPdCiMbFLBWu0eiZOJ7WoD+ptCLrmjZBK1Wk7I6D/I3NglUGOxg==
            }
        engines: { node: '>=12' }
        cpu: [x64]
        os: [openbsd]

    '@esbuild/openbsd-x64@0.25.4':
        resolution:
            {
                integrity: sha512-xAGGhyOQ9Otm1Xu8NT1ifGLnA6M3sJxZ6ixylb+vIUVzvvd6GOALpwQrYrtlPouMqd/vSbgehz6HaVk4+7Afhw==
            }
        engines: { node: '>=18' }
        cpu: [x64]
        os: [openbsd]

    '@esbuild/openbsd-x64@0.25.6':
        resolution:
            {
                integrity: sha512-hKrmDa0aOFOr71KQ/19JC7az1P0GWtCN1t2ahYAf4O007DHZt/dW8ym5+CUdJhQ/qkZmI1HAF8KkJbEFtCL7gw==
            }
        engines: { node: '>=18' }
        cpu: [x64]
        os: [openbsd]

    '@esbuild/openharmony-arm64@0.25.6':
        resolution:
            {
                integrity: sha512-+SqBcAWoB1fYKmpWoQP4pGtx+pUUC//RNYhFdbcSA16617cchuryuhOCRpPsjCblKukAckWsV+aQ3UKT/RMPcA==
            }
        engines: { node: '>=18' }
        cpu: [arm64]
        os: [openharmony]

    '@esbuild/sunos-x64@0.18.20':
        resolution:
            {
                integrity: sha512-kDbFRFp0YpTQVVrqUd5FTYmWo45zGaXe0X8E1G/LKFC0v8x0vWrhOWSLITcCn63lmZIxfOMXtCfti/RxN/0wnQ==
            }
        engines: { node: '>=12' }
        cpu: [x64]
        os: [sunos]

    '@esbuild/sunos-x64@0.25.4':
        resolution:
            {
                integrity: sha512-Mw+tzy4pp6wZEK0+Lwr76pWLjrtjmJyUB23tHKqEDP74R3q95luY/bXqXZeYl4NYlvwOqoRKlInQialgCKy67Q==
            }
        engines: { node: '>=18' }
        cpu: [x64]
        os: [sunos]

    '@esbuild/sunos-x64@0.25.6':
        resolution:
            {
                integrity: sha512-dyCGxv1/Br7MiSC42qinGL8KkG4kX0pEsdb0+TKhmJZgCUDBGmyo1/ArCjNGiOLiIAgdbWgmWgib4HoCi5t7kA==
            }
        engines: { node: '>=18' }
        cpu: [x64]
        os: [sunos]

    '@esbuild/win32-arm64@0.18.20':
        resolution:
            {
                integrity: sha512-ddYFR6ItYgoaq4v4JmQQaAI5s7npztfV4Ag6NrhiaW0RrnOXqBkgwZLofVTlq1daVTQNhtI5oieTvkRPfZrePg==
            }
        engines: { node: '>=12' }
        cpu: [arm64]
        os: [win32]

    '@esbuild/win32-arm64@0.25.4':
        resolution:
            {
                integrity: sha512-AVUP428VQTSddguz9dO9ngb+E5aScyg7nOeJDrF1HPYu555gmza3bDGMPhmVXL8svDSoqPCsCPjb265yG/kLKQ==
            }
        engines: { node: '>=18' }
        cpu: [arm64]
        os: [win32]

    '@esbuild/win32-arm64@0.25.6':
        resolution:
            {
                integrity: sha512-42QOgcZeZOvXfsCBJF5Afw73t4veOId//XD3i+/9gSkhSV6Gk3VPlWncctI+JcOyERv85FUo7RxuxGy+z8A43Q==
            }
        engines: { node: '>=18' }
        cpu: [arm64]
        os: [win32]

    '@esbuild/win32-ia32@0.18.20':
        resolution:
            {
                integrity: sha512-Wv7QBi3ID/rROT08SABTS7eV4hX26sVduqDOTe1MvGMjNd3EjOz4b7zeexIR62GTIEKrfJXKL9LFxTYgkyeu7g==
            }
        engines: { node: '>=12' }
        cpu: [ia32]
        os: [win32]

    '@esbuild/win32-ia32@0.25.4':
        resolution:
            {
                integrity: sha512-i1sW+1i+oWvQzSgfRcxxG2k4I9n3O9NRqy8U+uugaT2Dy7kLO9Y7wI72haOahxceMX8hZAzgGou1FhndRldxRg==
            }
        engines: { node: '>=18' }
        cpu: [ia32]
        os: [win32]

    '@esbuild/win32-ia32@0.25.6':
        resolution:
            {
                integrity: sha512-4AWhgXmDuYN7rJI6ORB+uU9DHLq/erBbuMoAuB4VWJTu5KtCgcKYPynF0YI1VkBNuEfjNlLrFr9KZPJzrtLkrQ==
            }
        engines: { node: '>=18' }
        cpu: [ia32]
        os: [win32]

    '@esbuild/win32-x64@0.18.20':
        resolution:
            {
                integrity: sha512-kTdfRcSiDfQca/y9QIkng02avJ+NCaQvrMejlsB3RRv5sE9rRoeBPISaZpKxHELzRxZyLvNts1P27W3wV+8geQ==
            }
        engines: { node: '>=12' }
        cpu: [x64]
        os: [win32]

    '@esbuild/win32-x64@0.25.4':
        resolution:
            {
                integrity: sha512-nOT2vZNw6hJ+z43oP1SPea/G/6AbN6X+bGNhNuq8NtRHy4wsMhw765IKLNmnjek7GvjWBYQ8Q5VBoYTFg9y1UQ==
            }
        engines: { node: '>=18' }
        cpu: [x64]
        os: [win32]

    '@esbuild/win32-x64@0.25.6':
        resolution:
            {
                integrity: sha512-NgJPHHbEpLQgDH2MjQu90pzW/5vvXIZ7KOnPyNBm92A6WgZ/7b6fJyUBjoumLqeOQQGqY2QjQxRo97ah4Sj0cA==
            }
        engines: { node: '>=18' }
        cpu: [x64]
        os: [win32]

    '@fastify/busboy@2.1.1':
        resolution:
            {
                integrity: sha512-vBZP4NlzfOlerQTnba4aqZoMhE/a9HY7HRqoOPaETQcSQuWEIyZMHGfVu6w9wGtGK5fED5qRs2DteVCjOH60sA==
            }
        engines: { node: '>=14' }

    '@img/sharp-darwin-arm64@0.33.5':
        resolution:
            {
                integrity: sha512-UT4p+iz/2H4twwAoLCqfA9UH5pI6DggwKEGuaPy7nCVQ8ZsiY5PIcrRvD1DzuY3qYL07NtIQcWnBSY/heikIFQ==
            }
        engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
        cpu: [arm64]
        os: [darwin]

    '@img/sharp-darwin-x64@0.33.5':
        resolution:
            {
                integrity: sha512-fyHac4jIc1ANYGRDxtiqelIbdWkIuQaI84Mv45KvGRRxSAa7o7d1ZKAOBaYbnepLC1WqxfpimdeWfvqqSGwR2Q==
            }
        engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
        cpu: [x64]
        os: [darwin]

    '@img/sharp-libvips-darwin-arm64@1.0.4':
        resolution:
            {
                integrity: sha512-XblONe153h0O2zuFfTAbQYAX2JhYmDHeWikp1LM9Hul9gVPjFY427k6dFEcOL72O01QxQsWi761svJ/ev9xEDg==
            }
        cpu: [arm64]
        os: [darwin]

    '@img/sharp-libvips-darwin-x64@1.0.4':
        resolution:
            {
                integrity: sha512-xnGR8YuZYfJGmWPvmlunFaWJsb9T/AO2ykoP3Fz/0X5XV2aoYBPkX6xqCQvUTKKiLddarLaxpzNe+b1hjeWHAQ==
            }
        cpu: [x64]
        os: [darwin]

    '@img/sharp-libvips-linux-arm64@1.0.4':
        resolution:
            {
                integrity: sha512-9B+taZ8DlyyqzZQnoeIvDVR/2F4EbMepXMc/NdVbkzsJbzkUjhXv/70GQJ7tdLA4YJgNP25zukcxpX2/SueNrA==
            }
        cpu: [arm64]
        os: [linux]

    '@img/sharp-libvips-linux-arm@1.0.5':
        resolution:
            {
                integrity: sha512-gvcC4ACAOPRNATg/ov8/MnbxFDJqf/pDePbBnuBDcjsI8PssmjoKMAz4LtLaVi+OnSb5FK/yIOamqDwGmXW32g==
            }
        cpu: [arm]
        os: [linux]

    '@img/sharp-libvips-linux-s390x@1.0.4':
        resolution:
            {
                integrity: sha512-u7Wz6ntiSSgGSGcjZ55im6uvTrOxSIS8/dgoVMoiGE9I6JAfU50yH5BoDlYA1tcuGS7g/QNtetJnxA6QEsCVTA==
            }
        cpu: [s390x]
        os: [linux]

    '@img/sharp-libvips-linux-x64@1.0.4':
        resolution:
            {
                integrity: sha512-MmWmQ3iPFZr0Iev+BAgVMb3ZyC4KeFc3jFxnNbEPas60e1cIfevbtuyf9nDGIzOaW9PdnDciJm+wFFaTlj5xYw==
            }
        cpu: [x64]
        os: [linux]

    '@img/sharp-libvips-linuxmusl-arm64@1.0.4':
        resolution:
            {
                integrity: sha512-9Ti+BbTYDcsbp4wfYib8Ctm1ilkugkA/uscUn6UXK1ldpC1JjiXbLfFZtRlBhjPZ5o1NCLiDbg8fhUPKStHoTA==
            }
        cpu: [arm64]
        os: [linux]

    '@img/sharp-libvips-linuxmusl-x64@1.0.4':
        resolution:
            {
                integrity: sha512-viYN1KX9m+/hGkJtvYYp+CCLgnJXwiQB39damAO7WMdKWlIhmYTfHjwSbQeUK/20vY154mwezd9HflVFM1wVSw==
            }
        cpu: [x64]
        os: [linux]

    '@img/sharp-linux-arm64@0.33.5':
        resolution:
            {
                integrity: sha512-JMVv+AMRyGOHtO1RFBiJy/MBsgz0x4AWrT6QoEVVTyh1E39TrCUpTRI7mx9VksGX4awWASxqCYLCV4wBZHAYxA==
            }
        engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
        cpu: [arm64]
        os: [linux]

    '@img/sharp-linux-arm@0.33.5':
        resolution:
            {
                integrity: sha512-JTS1eldqZbJxjvKaAkxhZmBqPRGmxgu+qFKSInv8moZ2AmT5Yib3EQ1c6gp493HvrvV8QgdOXdyaIBrhvFhBMQ==
            }
        engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
        cpu: [arm]
        os: [linux]

    '@img/sharp-linux-s390x@0.33.5':
        resolution:
            {
                integrity: sha512-y/5PCd+mP4CA/sPDKl2961b+C9d+vPAveS33s6Z3zfASk2j5upL6fXVPZi7ztePZ5CuH+1kW8JtvxgbuXHRa4Q==
            }
        engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
        cpu: [s390x]
        os: [linux]

    '@img/sharp-linux-x64@0.33.5':
        resolution:
            {
                integrity: sha512-opC+Ok5pRNAzuvq1AG0ar+1owsu842/Ab+4qvU879ippJBHvyY5n2mxF1izXqkPYlGuP/M556uh53jRLJmzTWA==
            }
        engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
        cpu: [x64]
        os: [linux]

    '@img/sharp-linuxmusl-arm64@0.33.5':
        resolution:
            {
                integrity: sha512-XrHMZwGQGvJg2V/oRSUfSAfjfPxO+4DkiRh6p2AFjLQztWUuY/o8Mq0eMQVIY7HJ1CDQUJlxGGZRw1a5bqmd1g==
            }
        engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
        cpu: [arm64]
        os: [linux]

    '@img/sharp-linuxmusl-x64@0.33.5':
        resolution:
            {
                integrity: sha512-WT+d/cgqKkkKySYmqoZ8y3pxx7lx9vVejxW/W4DOFMYVSkErR+w7mf2u8m/y4+xHe7yY9DAXQMWQhpnMuFfScw==
            }
        engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
        cpu: [x64]
        os: [linux]

    '@img/sharp-wasm32@0.33.5':
        resolution:
            {
                integrity: sha512-ykUW4LVGaMcU9lu9thv85CbRMAwfeadCJHRsg2GmeRa/cJxsVY9Rbd57JcMxBkKHag5U/x7TSBpScF4U8ElVzg==
            }
        engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
        cpu: [wasm32]

    '@img/sharp-win32-ia32@0.33.5':
        resolution:
            {
                integrity: sha512-T36PblLaTwuVJ/zw/LaH0PdZkRz5rd3SmMHX8GSmR7vtNSP5Z6bQkExdSK7xGWyxLw4sUknBuugTelgw2faBbQ==
            }
        engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
        cpu: [ia32]
        os: [win32]

    '@img/sharp-win32-x64@0.33.5':
        resolution:
            {
                integrity: sha512-MpY/o8/8kj+EcnxwvrP4aTJSWw/aZ7JIGR4aBeZkZw5B7/Jn+tY9/VNwtcoGmdT7GfggGIU4kygOMSbYnOrAbg==
            }
        engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
        cpu: [x64]
        os: [win32]

    '@jridgewell/resolve-uri@3.1.2':
        resolution:
            {
                integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==
            }
        engines: { node: '>=6.0.0' }

    '@jridgewell/sourcemap-codec@1.5.4':
        resolution:
            {
                integrity: sha512-VT2+G1VQs/9oz078bLrYbecdZKs912zQlkelYpuf+SXF+QvZDYJlbx/LSx+meSAwdDFnF8FVXW92AVjjkVmgFw==
            }

    '@jridgewell/trace-mapping@0.3.9':
        resolution:
            {
                integrity: sha512-3Belt6tdc8bPgAtbcmdtNJlirVoTmEb5e2gC94PnkwEW9jI6CAHUeoG85tjWP5WquqfavoMtMwiG4P926ZKKuQ==
            }

    '@poppinss/colors@4.1.5':
        resolution:
            {
                integrity: sha512-FvdDqtcRCtz6hThExcFOgW0cWX+xwSMWcRuQe5ZEb2m7cVQOAVZOIMt+/v9RxGiD9/OY16qJBXK4CVKWAPalBw==
            }

    '@poppinss/dumper@0.6.4':
        resolution:
            {
                integrity: sha512-iG0TIdqv8xJ3Lt9O8DrPRxw1MRLjNpoqiSGU03P/wNLP/s0ra0udPJ1J2Tx5M0J3H/cVyEgpbn8xUKRY9j59kQ==
            }

    '@poppinss/exception@1.2.2':
        resolution:
            {
                integrity: sha512-m7bpKCD4QMlFCjA/nKTs23fuvoVFoA83brRKmObCUNmi/9tVu8Ve3w4YQAnJu4q3Tjf5fr685HYIC/IA2zHRSg==
            }

    '@sindresorhus/is@7.0.2':
        resolution:
            {
                integrity: sha512-d9xRovfKNz1SKieM0qJdO+PQonjnnIfSNWfHYnBSJ9hkjm0ZPw6HlxscDXYstp3z+7V2GOFHc+J0CYrYTjqCJw==
            }
        engines: { node: '>=18' }

    '@speed-highlight/core@1.2.7':
        resolution:
            {
                integrity: sha512-0dxmVj4gxg3Jg879kvFS/msl4s9F3T9UXC1InxgOf7t5NvcPD97u/WTA5vL/IxWHMn7qSxBozqrnnE2wvl1m8g==
            }

    acorn-walk@8.3.2:
        resolution:
            {
                integrity: sha512-cjkyv4OtNCIeqhHrfS81QWXoCBPExR/J62oyEqepVw8WaQeSqpW2uhuLPh1m9eWhDuOo/jUXVTlifvesOWp/4A==
            }
        engines: { node: '>=0.4.0' }

    acorn@8.14.0:
        resolution:
            {
                integrity: sha512-cl669nCJTZBsL97OF4kUQm5g5hC2uihk0NxY3WENAC0TYdILVkAyHymAntgxGkl7K+t0cXIrH5siy5S4XkFycA==
            }
        engines: { node: '>=0.4.0' }
        hasBin: true

    blake3-wasm@2.1.5:
        resolution:
            {
                integrity: sha512-F1+K8EbfOZE49dtoPtmxUQrpXaBIl3ICvasLh+nJta0xkz+9kF/7uet9fLnwKqhDrmj6g+6K3Tw9yQPUg2ka5g==
            }

    buffer-from@1.1.2:
        resolution:
            {
                integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==
            }

    color-convert@2.0.1:
        resolution:
            {
                integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==
            }
        engines: { node: '>=7.0.0' }

    color-name@1.1.4:
        resolution:
            {
                integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==
            }

    color-string@1.9.1:
        resolution:
            {
                integrity: sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==
            }

    color@4.2.3:
        resolution:
            {
                integrity: sha512-1rXeuUUiGGrykh+CeBdu5Ie7OJwinCgQY0bc7GCRxy5xVHy+moaqkpL/jqQq0MtQOeYcrqEz4abc5f0KtU7W4A==
            }
        engines: { node: '>=12.5.0' }

    cookie@1.0.2:
        resolution:
            {
                integrity: sha512-9Kr/j4O16ISv8zBBhJoi4bXOYNTkFLOqSL3UDB0njXxCXNezjeyVrJyGOWtgfs/q2km1gwBcfH8q1yEGoMYunA==
            }
        engines: { node: '>=18' }

    debug@4.4.1:
        resolution:
            {
                integrity: sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==
            }
        engines: { node: '>=6.0' }
        peerDependencies:
            supports-color: '*'
        peerDependenciesMeta:
            supports-color:
                optional: true

    defu@6.1.4:
        resolution:
            {
                integrity: sha512-mEQCMmwJu317oSz8CwdIOdwf3xMif1ttiM8LTufzc3g6kR+9Pe236twL8j3IYT1F7GfRgGcW6MWxzZjLIkuHIg==
            }

    detect-libc@2.0.4:
        resolution:
            {
                integrity: sha512-3UDv+G9CsCKO1WKMGw9fwq/SWJYbI0c5Y7LU1AXYoDdbhE2AHQ6N6Nb34sG8Fj7T5APy8qXDCKuuIHd1BR0tVA==
            }
        engines: { node: '>=8' }

    drizzle-kit@0.31.4:
        resolution:
            {
                integrity: sha512-tCPWVZWZqWVx2XUsVpJRnH9Mx0ClVOf5YUHerZ5so1OKSlqww4zy1R5ksEdGRcO3tM3zj0PYN6V48TbQCL1RfA==
            }
        hasBin: true

    drizzle-orm@0.44.2:
        resolution:
            {
                integrity: sha512-zGAqBzWWkVSFjZpwPOrmCrgO++1kZ5H/rZ4qTGeGOe18iXGVJWf3WPfHOVwFIbmi8kHjfJstC6rJomzGx8g/dQ==
            }
        peerDependencies:
            '@aws-sdk/client-rds-data': '>=3'
            '@cloudflare/workers-types': '>=4'
            '@electric-sql/pglite': '>=0.2.0'
            '@libsql/client': '>=0.10.0'
            '@libsql/client-wasm': '>=0.10.0'
            '@neondatabase/serverless': '>=0.10.0'
            '@op-engineering/op-sqlite': '>=2'
            '@opentelemetry/api': ^1.4.1
            '@planetscale/database': '>=1.13'
            '@prisma/client': '*'
            '@tidbcloud/serverless': '*'
            '@types/better-sqlite3': '*'
            '@types/pg': '*'
            '@types/sql.js': '*'
            '@upstash/redis': '>=1.34.7'
            '@vercel/postgres': '>=0.8.0'
            '@xata.io/client': '*'
            better-sqlite3: '>=7'
            bun-types: '*'
            expo-sqlite: '>=14.0.0'
            gel: '>=2'
            knex: '*'
            kysely: '*'
            mysql2: '>=2'
            pg: '>=8'
            postgres: '>=3'
            prisma: '*'
            sql.js: '>=1'
            sqlite3: '>=5'
        peerDependenciesMeta:
            '@aws-sdk/client-rds-data':
                optional: true
            '@cloudflare/workers-types':
                optional: true
            '@electric-sql/pglite':
                optional: true
            '@libsql/client':
                optional: true
            '@libsql/client-wasm':
                optional: true
            '@neondatabase/serverless':
                optional: true
            '@op-engineering/op-sqlite':
                optional: true
            '@opentelemetry/api':
                optional: true
            '@planetscale/database':
                optional: true
            '@prisma/client':
                optional: true
            '@tidbcloud/serverless':
                optional: true
            '@types/better-sqlite3':
                optional: true
            '@types/pg':
                optional: true
            '@types/sql.js':
                optional: true
            '@upstash/redis':
                optional: true
            '@vercel/postgres':
                optional: true
            '@xata.io/client':
                optional: true
            better-sqlite3:
                optional: true
            bun-types:
                optional: true
            expo-sqlite:
                optional: true
            gel:
                optional: true
            knex:
                optional: true
            kysely:
                optional: true
            mysql2:
                optional: true
            pg:
                optional: true
            postgres:
                optional: true
            prisma:
                optional: true
            sql.js:
                optional: true
            sqlite3:
                optional: true

    error-stack-parser-es@1.0.5:
        resolution:
            {
                integrity: sha512-5qucVt2XcuGMcEGgWI7i+yZpmpByQ8J1lHhcL7PwqCwu9FPP3VUXzT4ltHe5i2z9dePwEHcDVOAfSnHsOlCXRA==
            }

    esbuild-register@3.6.0:
        resolution:
            {
                integrity: sha512-H2/S7Pm8a9CL1uhp9OvjwrBh5Pvx0H8qVOxNu8Wed9Y7qv56MPtq+GGM8RJpq6glYJn9Wspr8uw7l55uyinNeg==
            }
        peerDependencies:
            esbuild: '>=0.12 <1'

    esbuild@0.18.20:
        resolution:
            {
                integrity: sha512-ceqxoedUrcayh7Y7ZX6NdbbDzGROiyVBgC4PriJThBKSVPWnnFHZAkfI1lJT8QFkOwH4qOS2SJkS4wvpGl8BpA==
            }
        engines: { node: '>=12' }
        hasBin: true

    esbuild@0.25.4:
        resolution:
            {
                integrity: sha512-8pgjLUcUjcgDg+2Q4NYXnPbo/vncAY4UmyaCm0jZevERqCHZIaWwdJHkf8XQtu4AxSKCdvrUbT0XUr1IdZzI8Q==
            }
        engines: { node: '>=18' }
        hasBin: true

    esbuild@0.25.6:
        resolution:
            {
                integrity: sha512-GVuzuUwtdsghE3ocJ9Bs8PNoF13HNQ5TXbEi2AhvVb8xU1Iwt9Fos9FEamfoee+u/TOsn7GUWc04lz46n2bbTg==
            }
        engines: { node: '>=18' }
        hasBin: true

    exit-hook@2.2.1:
        resolution:
            {
                integrity: sha512-eNTPlAD67BmP31LDINZ3U7HSF8l57TxOY2PmBJ1shpCvpnxBF93mWCE8YHBnXs8qiUZJc9WDcWIeC3a2HIAMfw==
            }
        engines: { node: '>=6' }

    exsolve@1.0.7:
        resolution:
            {
                integrity: sha512-VO5fQUzZtI6C+vx4w/4BWJpg3s/5l+6pRQEHzFRM8WFi4XffSP1Z+4qi7GbjWbvRQEbdIco5mIMq+zX4rPuLrw==
            }

    fsevents@2.3.3:
        resolution:
            {
                integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==
            }
        engines: { node: ^8.16.0 || ^10.6.0 || >=11.0.0 }
        os: [darwin]

    get-tsconfig@4.10.1:
        resolution:
            {
                integrity: sha512-auHyJ4AgMz7vgS8Hp3N6HXSmlMdUyhSUrfBF16w153rxtLIEOE+HGqaBppczZvnHLqQJfiHotCYpNhl0lUROFQ==
            }

    glob-to-regexp@0.4.1:
        resolution:
            {
                integrity: sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw==
            }

    is-arrayish@0.3.2:
        resolution:
            {
                integrity: sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==
            }

    kleur@4.1.5:
        resolution:
            {
                integrity: sha512-o+NO+8WrRiQEE4/7nwRJhN1HWpVmJm511pBHUxPLtp0BUISzlBplORYSmTclCnJvQq2tKu/sgl3xVpkc7ZWuQQ==
            }
        engines: { node: '>=6' }

    mime@3.0.0:
        resolution:
            {
                integrity: sha512-jSCU7/VB1loIWBZe14aEYHU/+1UMEHoaO7qxCOVJOw9GgH72VAWppxNcjU+x9a2k3GSIBXNKxXQFqRvvZ7vr3A==
            }
        engines: { node: '>=10.0.0' }
        hasBin: true

    miniflare@4.20250709.0:
        resolution:
            {
                integrity: sha512-dRGXi6Do9ArQZt7205QGWZ1tD6k6xQNY/mAZBAtiaQYvKxFuNyiHYlFnSN8Co4AFCVOozo/U52sVAaHvlcmnew==
            }
        engines: { node: '>=18.0.0' }
        hasBin: true

    ms@2.1.3:
        resolution:
            {
                integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==
            }

    ohash@2.0.11:
        resolution:
            {
                integrity: sha512-RdR9FQrFwNBNXAr4GixM8YaRZRJ5PUWbKYbE5eOsrwAjJW0q2REGcf79oYPsLyskQCZG1PLN+S/K1V00joZAoQ==
            }

    path-to-regexp@6.3.0:
        resolution:
            {
                integrity: sha512-Yhpw4T9C6hPpgPeA28us07OJeqZ5EzQTkbfwuhsUg0c237RomFoETJgmp2sa3F/41gfLE6G5cqcYwznmeEeOlQ==
            }

    pathe@2.0.3:
        resolution:
            {
                integrity: sha512-WUjGcAqP1gQacoQe+OBJsFA7Ld4DyXuUIjZ5cc75cLHvJ7dtNsTugphxIADwspS+AraAUePCKrSVtPLFj/F88w==
            }

    prettier@3.6.2:
        resolution:
            {
                integrity: sha512-I7AIg5boAr5R0FFtJ6rCfD+LFsWHp81dolrFD8S79U9tb8Az2nGrJncnMSnys+bpQJfRUzqs9hnA81OAA3hCuQ==
            }
        engines: { node: '>=14' }
        hasBin: true

    resolve-pkg-maps@1.0.0:
        resolution:
            {
                integrity: sha512-seS2Tj26TBVOC2NIc2rOe2y2ZO7efxITtLZcGSOnHHNOQ7CkiUBfw0Iw2ck6xkIhPwLhKNLS8BO+hEpngQlqzw==
            }

    semver@7.7.2:
        resolution:
            {
                integrity: sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==
            }
        engines: { node: '>=10' }
        hasBin: true

    sharp@0.33.5:
        resolution:
            {
                integrity: sha512-haPVm1EkS9pgvHrQ/F3Xy+hgcuMV0Wm9vfIBSiwZ05k+xgb0PkBQpGsAA/oWdDobNaZTH5ppvHtzCFbnSEwHVw==
            }
        engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }

    simple-swizzle@0.2.2:
        resolution:
            {
                integrity: sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==
            }

    source-map-support@0.5.21:
        resolution:
            {
                integrity: sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==
            }

    source-map@0.6.1:
        resolution:
            {
                integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==
            }
        engines: { node: '>=0.10.0' }

    stoppable@1.1.0:
        resolution:
            {
                integrity: sha512-KXDYZ9dszj6bzvnEMRYvxgeTHU74QBFL54XKtP3nyMuJ81CFYtABZ3bAzL2EdFUaEwJOBOgENyFj3R7oTzDyyw==
            }
        engines: { node: '>=4', npm: '>=6' }

    supports-color@10.0.0:
        resolution:
            {
                integrity: sha512-HRVVSbCCMbj7/kdWF9Q+bbckjBHLtHMEoJWlkmYzzdwhYMkjkOwubLM6t7NbWKjgKamGDrWL1++KrjUO1t9oAQ==
            }
        engines: { node: '>=18' }

    tslib@2.8.1:
        resolution:
            {
                integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==
            }

    typescript@5.8.3:
        resolution:
            {
                integrity: sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==
            }
        engines: { node: '>=14.17' }
        hasBin: true

    ufo@1.6.1:
        resolution:
            {
                integrity: sha512-9a4/uxlTWJ4+a5i0ooc1rU7C7YOw3wT+UGqdeNNHWnOF9qcMBgLRS+4IYUqbczewFx4mLEig6gawh7X6mFlEkA==
            }

    undici@5.29.0:
        resolution:
            {
                integrity: sha512-raqeBD6NQK4SkWhQzeYKd1KmIG6dllBOTt55Rmkt4HtI9mwdWtJljnrXjAFUBLTSN67HWrOIZ3EPF4kjUw80Bg==
            }
        engines: { node: '>=14.0' }

    unenv@2.0.0-rc.17:
        resolution:
            {
                integrity: sha512-B06u0wXkEd+o5gOCMl/ZHl5cfpYbDZKAT+HWTL+Hws6jWu7dCiqBBXXXzMFcFVJb8D4ytAnYmxJA83uwOQRSsg==
            }

    workerd@1.20250709.0:
        resolution:
            {
                integrity: sha512-BqLPpmvRN+TYUSG61OkWamsGdEuMwgvabP8m0QOHIfofnrD2YVyWqE1kXJ0GH5EsVEuWamE5sR8XpTfsGBmIpg==
            }
        engines: { node: '>=16' }
        hasBin: true

    wrangler@4.24.1:
        resolution:
            {
                integrity: sha512-n7d5BQyOQU7WyEYMC3zNarWzsqWttsusP6qWz4rbbydmnWKSP1wLtsv0yKiyz/LYFumpolz48d5phHp04nR6uw==
            }
        engines: { node: '>=18.0.0' }
        hasBin: true
        peerDependencies:
            '@cloudflare/workers-types': ^4.20250709.0
        peerDependenciesMeta:
            '@cloudflare/workers-types':
                optional: true

    ws@8.18.0:
        resolution:
            {
                integrity: sha512-8VbfWfHLbbwu3+N6OKsOMpBdT4kXPDDB9cJk2bJ6mh9ucxdlnNvH1e+roYkKmN9Nxw2yjz7VzeO9oOz2zJ04Pw==
            }
        engines: { node: '>=10.0.0' }
        peerDependencies:
            bufferutil: ^4.0.1
            utf-8-validate: '>=5.0.2'
        peerDependenciesMeta:
            bufferutil:
                optional: true
            utf-8-validate:
                optional: true

    youch-core@0.3.3:
        resolution:
            {
                integrity: sha512-ho7XuGjLaJ2hWHoK8yFnsUGy2Y5uDpqSTq1FkHLK4/oqKtyUU1AFbOOxY4IpC9f0fTLjwYbslUz0Po5BpD1wrA==
            }

    youch@4.1.0-beta.10:
        resolution:
            {
                integrity: sha512-rLfVLB4FgQneDr0dv1oddCVZmKjcJ6yX6mS4pU82Mq/Dt9a3cLZQ62pDBL4AUO+uVrCvtWz3ZFUL2HFAFJ/BXQ==
            }

    zod@3.22.3:
        resolution:
            {
                integrity: sha512-EjIevzuJRiRPbVH4mGc8nApb/lVLKVpmUhAaR5R5doKGfAnGJ6Gr3CViAVjP+4FWSxCsybeWQdcgCtbX+7oZug==
            }

snapshots:
    '@cloudflare/kv-asset-handler@0.4.0':
        dependencies:
            mime: 3.0.0

    '@cloudflare/unenv-preset@2.3.3(unenv@2.0.0-rc.17)(workerd@1.20250709.0)':
        dependencies:
            unenv: 2.0.0-rc.17
        optionalDependencies:
            workerd: 1.20250709.0

    '@cloudflare/workerd-darwin-64@1.20250709.0':
        optional: true

    '@cloudflare/workerd-darwin-arm64@1.20250709.0':
        optional: true

    '@cloudflare/workerd-linux-64@1.20250709.0':
        optional: true

    '@cloudflare/workerd-linux-arm64@1.20250709.0':
        optional: true

    '@cloudflare/workerd-windows-64@1.20250709.0':
        optional: true

    '@cspotcode/source-map-support@0.8.1':
        dependencies:
            '@jridgewell/trace-mapping': 0.3.9

    '@drizzle-team/brocli@0.10.2': {}

    '@emnapi/runtime@1.4.4':
        dependencies:
            tslib: 2.8.1
        optional: true

    '@esbuild-kit/core-utils@3.3.2':
        dependencies:
            esbuild: 0.18.20
            source-map-support: 0.5.21

    '@esbuild-kit/esm-loader@2.6.5':
        dependencies:
            '@esbuild-kit/core-utils': 3.3.2
            get-tsconfig: 4.10.1

    '@esbuild/aix-ppc64@0.25.4':
        optional: true

    '@esbuild/aix-ppc64@0.25.6':
        optional: true

    '@esbuild/android-arm64@0.18.20':
        optional: true

    '@esbuild/android-arm64@0.25.4':
        optional: true

    '@esbuild/android-arm64@0.25.6':
        optional: true

    '@esbuild/android-arm@0.18.20':
        optional: true

    '@esbuild/android-arm@0.25.4':
        optional: true

    '@esbuild/android-arm@0.25.6':
        optional: true

    '@esbuild/android-x64@0.18.20':
        optional: true

    '@esbuild/android-x64@0.25.4':
        optional: true

    '@esbuild/android-x64@0.25.6':
        optional: true

    '@esbuild/darwin-arm64@0.18.20':
        optional: true

    '@esbuild/darwin-arm64@0.25.4':
        optional: true

    '@esbuild/darwin-arm64@0.25.6':
        optional: true

    '@esbuild/darwin-x64@0.18.20':
        optional: true

    '@esbuild/darwin-x64@0.25.4':
        optional: true

    '@esbuild/darwin-x64@0.25.6':
        optional: true

    '@esbuild/freebsd-arm64@0.18.20':
        optional: true

    '@esbuild/freebsd-arm64@0.25.4':
        optional: true

    '@esbuild/freebsd-arm64@0.25.6':
        optional: true

    '@esbuild/freebsd-x64@0.18.20':
        optional: true

    '@esbuild/freebsd-x64@0.25.4':
        optional: true

    '@esbuild/freebsd-x64@0.25.6':
        optional: true

    '@esbuild/linux-arm64@0.18.20':
        optional: true

    '@esbuild/linux-arm64@0.25.4':
        optional: true

    '@esbuild/linux-arm64@0.25.6':
        optional: true

    '@esbuild/linux-arm@0.18.20':
        optional: true

    '@esbuild/linux-arm@0.25.4':
        optional: true

    '@esbuild/linux-arm@0.25.6':
        optional: true

    '@esbuild/linux-ia32@0.18.20':
        optional: true

    '@esbuild/linux-ia32@0.25.4':
        optional: true

    '@esbuild/linux-ia32@0.25.6':
        optional: true

    '@esbuild/linux-loong64@0.18.20':
        optional: true

    '@esbuild/linux-loong64@0.25.4':
        optional: true

    '@esbuild/linux-loong64@0.25.6':
        optional: true

    '@esbuild/linux-mips64el@0.18.20':
        optional: true

    '@esbuild/linux-mips64el@0.25.4':
        optional: true

    '@esbuild/linux-mips64el@0.25.6':
        optional: true

    '@esbuild/linux-ppc64@0.18.20':
        optional: true

    '@esbuild/linux-ppc64@0.25.4':
        optional: true

    '@esbuild/linux-ppc64@0.25.6':
        optional: true

    '@esbuild/linux-riscv64@0.18.20':
        optional: true

    '@esbuild/linux-riscv64@0.25.4':
        optional: true

    '@esbuild/linux-riscv64@0.25.6':
        optional: true

    '@esbuild/linux-s390x@0.18.20':
        optional: true

    '@esbuild/linux-s390x@0.25.4':
        optional: true

    '@esbuild/linux-s390x@0.25.6':
        optional: true

    '@esbuild/linux-x64@0.18.20':
        optional: true

    '@esbuild/linux-x64@0.25.4':
        optional: true

    '@esbuild/linux-x64@0.25.6':
        optional: true

    '@esbuild/netbsd-arm64@0.25.4':
        optional: true

    '@esbuild/netbsd-arm64@0.25.6':
        optional: true

    '@esbuild/netbsd-x64@0.18.20':
        optional: true

    '@esbuild/netbsd-x64@0.25.4':
        optional: true

    '@esbuild/netbsd-x64@0.25.6':
        optional: true

    '@esbuild/openbsd-arm64@0.25.4':
        optional: true

    '@esbuild/openbsd-arm64@0.25.6':
        optional: true

    '@esbuild/openbsd-x64@0.18.20':
        optional: true

    '@esbuild/openbsd-x64@0.25.4':
        optional: true

    '@esbuild/openbsd-x64@0.25.6':
        optional: true

    '@esbuild/openharmony-arm64@0.25.6':
        optional: true

    '@esbuild/sunos-x64@0.18.20':
        optional: true

    '@esbuild/sunos-x64@0.25.4':
        optional: true

    '@esbuild/sunos-x64@0.25.6':
        optional: true

    '@esbuild/win32-arm64@0.18.20':
        optional: true

    '@esbuild/win32-arm64@0.25.4':
        optional: true

    '@esbuild/win32-arm64@0.25.6':
        optional: true

    '@esbuild/win32-ia32@0.18.20':
        optional: true

    '@esbuild/win32-ia32@0.25.4':
        optional: true

    '@esbuild/win32-ia32@0.25.6':
        optional: true

    '@esbuild/win32-x64@0.18.20':
        optional: true

    '@esbuild/win32-x64@0.25.4':
        optional: true

    '@esbuild/win32-x64@0.25.6':
        optional: true

    '@fastify/busboy@2.1.1': {}

    '@img/sharp-darwin-arm64@0.33.5':
        optionalDependencies:
            '@img/sharp-libvips-darwin-arm64': 1.0.4
        optional: true

    '@img/sharp-darwin-x64@0.33.5':
        optionalDependencies:
            '@img/sharp-libvips-darwin-x64': 1.0.4
        optional: true

    '@img/sharp-libvips-darwin-arm64@1.0.4':
        optional: true

    '@img/sharp-libvips-darwin-x64@1.0.4':
        optional: true

    '@img/sharp-libvips-linux-arm64@1.0.4':
        optional: true

    '@img/sharp-libvips-linux-arm@1.0.5':
        optional: true

    '@img/sharp-libvips-linux-s390x@1.0.4':
        optional: true

    '@img/sharp-libvips-linux-x64@1.0.4':
        optional: true

    '@img/sharp-libvips-linuxmusl-arm64@1.0.4':
        optional: true

    '@img/sharp-libvips-linuxmusl-x64@1.0.4':
        optional: true

    '@img/sharp-linux-arm64@0.33.5':
        optionalDependencies:
            '@img/sharp-libvips-linux-arm64': 1.0.4
        optional: true

    '@img/sharp-linux-arm@0.33.5':
        optionalDependencies:
            '@img/sharp-libvips-linux-arm': 1.0.5
        optional: true

    '@img/sharp-linux-s390x@0.33.5':
        optionalDependencies:
            '@img/sharp-libvips-linux-s390x': 1.0.4
        optional: true

    '@img/sharp-linux-x64@0.33.5':
        optionalDependencies:
            '@img/sharp-libvips-linux-x64': 1.0.4
        optional: true

    '@img/sharp-linuxmusl-arm64@0.33.5':
        optionalDependencies:
            '@img/sharp-libvips-linuxmusl-arm64': 1.0.4
        optional: true

    '@img/sharp-linuxmusl-x64@0.33.5':
        optionalDependencies:
            '@img/sharp-libvips-linuxmusl-x64': 1.0.4
        optional: true

    '@img/sharp-wasm32@0.33.5':
        dependencies:
            '@emnapi/runtime': 1.4.4
        optional: true

    '@img/sharp-win32-ia32@0.33.5':
        optional: true

    '@img/sharp-win32-x64@0.33.5':
        optional: true

    '@jridgewell/resolve-uri@3.1.2': {}

    '@jridgewell/sourcemap-codec@1.5.4': {}

    '@jridgewell/trace-mapping@0.3.9':
        dependencies:
            '@jridgewell/resolve-uri': 3.1.2
            '@jridgewell/sourcemap-codec': 1.5.4

    '@poppinss/colors@4.1.5':
        dependencies:
            kleur: 4.1.5

    '@poppinss/dumper@0.6.4':
        dependencies:
            '@poppinss/colors': 4.1.5
            '@sindresorhus/is': 7.0.2
            supports-color: 10.0.0

    '@poppinss/exception@1.2.2': {}

    '@sindresorhus/is@7.0.2': {}

    '@speed-highlight/core@1.2.7': {}

    acorn-walk@8.3.2: {}

    acorn@8.14.0: {}

    blake3-wasm@2.1.5: {}

    buffer-from@1.1.2: {}

    color-convert@2.0.1:
        dependencies:
            color-name: 1.1.4

    color-name@1.1.4: {}

    color-string@1.9.1:
        dependencies:
            color-name: 1.1.4
            simple-swizzle: 0.2.2

    color@4.2.3:
        dependencies:
            color-convert: 2.0.1
            color-string: 1.9.1

    cookie@1.0.2: {}

    debug@4.4.1:
        dependencies:
            ms: 2.1.3

    defu@6.1.4: {}

    detect-libc@2.0.4: {}

    drizzle-kit@0.31.4:
        dependencies:
            '@drizzle-team/brocli': 0.10.2
            '@esbuild-kit/esm-loader': 2.6.5
            esbuild: 0.25.6
            esbuild-register: 3.6.0(esbuild@0.25.6)
        transitivePeerDependencies:
            - supports-color

    drizzle-orm@0.44.2: {}

    error-stack-parser-es@1.0.5: {}

    esbuild-register@3.6.0(esbuild@0.25.6):
        dependencies:
            debug: 4.4.1
            esbuild: 0.25.6
        transitivePeerDependencies:
            - supports-color

    esbuild@0.18.20:
        optionalDependencies:
            '@esbuild/android-arm': 0.18.20
            '@esbuild/android-arm64': 0.18.20
            '@esbuild/android-x64': 0.18.20
            '@esbuild/darwin-arm64': 0.18.20
            '@esbuild/darwin-x64': 0.18.20
            '@esbuild/freebsd-arm64': 0.18.20
            '@esbuild/freebsd-x64': 0.18.20
            '@esbuild/linux-arm': 0.18.20
            '@esbuild/linux-arm64': 0.18.20
            '@esbuild/linux-ia32': 0.18.20
            '@esbuild/linux-loong64': 0.18.20
            '@esbuild/linux-mips64el': 0.18.20
            '@esbuild/linux-ppc64': 0.18.20
            '@esbuild/linux-riscv64': 0.18.20
            '@esbuild/linux-s390x': 0.18.20
            '@esbuild/linux-x64': 0.18.20
            '@esbuild/netbsd-x64': 0.18.20
            '@esbuild/openbsd-x64': 0.18.20
            '@esbuild/sunos-x64': 0.18.20
            '@esbuild/win32-arm64': 0.18.20
            '@esbuild/win32-ia32': 0.18.20
            '@esbuild/win32-x64': 0.18.20

    esbuild@0.25.4:
        optionalDependencies:
            '@esbuild/aix-ppc64': 0.25.4
            '@esbuild/android-arm': 0.25.4
            '@esbuild/android-arm64': 0.25.4
            '@esbuild/android-x64': 0.25.4
            '@esbuild/darwin-arm64': 0.25.4
            '@esbuild/darwin-x64': 0.25.4
            '@esbuild/freebsd-arm64': 0.25.4
            '@esbuild/freebsd-x64': 0.25.4
            '@esbuild/linux-arm': 0.25.4
            '@esbuild/linux-arm64': 0.25.4
            '@esbuild/linux-ia32': 0.25.4
            '@esbuild/linux-loong64': 0.25.4
            '@esbuild/linux-mips64el': 0.25.4
            '@esbuild/linux-ppc64': 0.25.4
            '@esbuild/linux-riscv64': 0.25.4
            '@esbuild/linux-s390x': 0.25.4
            '@esbuild/linux-x64': 0.25.4
            '@esbuild/netbsd-arm64': 0.25.4
            '@esbuild/netbsd-x64': 0.25.4
            '@esbuild/openbsd-arm64': 0.25.4
            '@esbuild/openbsd-x64': 0.25.4
            '@esbuild/sunos-x64': 0.25.4
            '@esbuild/win32-arm64': 0.25.4
            '@esbuild/win32-ia32': 0.25.4
            '@esbuild/win32-x64': 0.25.4

    esbuild@0.25.6:
        optionalDependencies:
            '@esbuild/aix-ppc64': 0.25.6
            '@esbuild/android-arm': 0.25.6
            '@esbuild/android-arm64': 0.25.6
            '@esbuild/android-x64': 0.25.6
            '@esbuild/darwin-arm64': 0.25.6
            '@esbuild/darwin-x64': 0.25.6
            '@esbuild/freebsd-arm64': 0.25.6
            '@esbuild/freebsd-x64': 0.25.6
            '@esbuild/linux-arm': 0.25.6
            '@esbuild/linux-arm64': 0.25.6
            '@esbuild/linux-ia32': 0.25.6
            '@esbuild/linux-loong64': 0.25.6
            '@esbuild/linux-mips64el': 0.25.6
            '@esbuild/linux-ppc64': 0.25.6
            '@esbuild/linux-riscv64': 0.25.6
            '@esbuild/linux-s390x': 0.25.6
            '@esbuild/linux-x64': 0.25.6
            '@esbuild/netbsd-arm64': 0.25.6
            '@esbuild/netbsd-x64': 0.25.6
            '@esbuild/openbsd-arm64': 0.25.6
            '@esbuild/openbsd-x64': 0.25.6
            '@esbuild/openharmony-arm64': 0.25.6
            '@esbuild/sunos-x64': 0.25.6
            '@esbuild/win32-arm64': 0.25.6
            '@esbuild/win32-ia32': 0.25.6
            '@esbuild/win32-x64': 0.25.6

    exit-hook@2.2.1: {}

    exsolve@1.0.7: {}

    fsevents@2.3.3:
        optional: true

    get-tsconfig@4.10.1:
        dependencies:
            resolve-pkg-maps: 1.0.0

    glob-to-regexp@0.4.1: {}

    is-arrayish@0.3.2: {}

    kleur@4.1.5: {}

    mime@3.0.0: {}

    miniflare@4.20250709.0:
        dependencies:
            '@cspotcode/source-map-support': 0.8.1
            acorn: 8.14.0
            acorn-walk: 8.3.2
            exit-hook: 2.2.1
            glob-to-regexp: 0.4.1
            sharp: 0.33.5
            stoppable: 1.1.0
            undici: 5.29.0
            workerd: 1.20250709.0
            ws: 8.18.0
            youch: 4.1.0-beta.10
            zod: 3.22.3
        transitivePeerDependencies:
            - bufferutil
            - utf-8-validate

    ms@2.1.3: {}

    ohash@2.0.11: {}

    path-to-regexp@6.3.0: {}

    pathe@2.0.3: {}

    prettier@3.6.2: {}

    resolve-pkg-maps@1.0.0: {}

    semver@7.7.2: {}

    sharp@0.33.5:
        dependencies:
            color: 4.2.3
            detect-libc: 2.0.4
            semver: 7.7.2
        optionalDependencies:
            '@img/sharp-darwin-arm64': 0.33.5
            '@img/sharp-darwin-x64': 0.33.5
            '@img/sharp-libvips-darwin-arm64': 1.0.4
            '@img/sharp-libvips-darwin-x64': 1.0.4
            '@img/sharp-libvips-linux-arm': 1.0.5
            '@img/sharp-libvips-linux-arm64': 1.0.4
            '@img/sharp-libvips-linux-s390x': 1.0.4
            '@img/sharp-libvips-linux-x64': 1.0.4
            '@img/sharp-libvips-linuxmusl-arm64': 1.0.4
            '@img/sharp-libvips-linuxmusl-x64': 1.0.4
            '@img/sharp-linux-arm': 0.33.5
            '@img/sharp-linux-arm64': 0.33.5
            '@img/sharp-linux-s390x': 0.33.5
            '@img/sharp-linux-x64': 0.33.5
            '@img/sharp-linuxmusl-arm64': 0.33.5
            '@img/sharp-linuxmusl-x64': 0.33.5
            '@img/sharp-wasm32': 0.33.5
            '@img/sharp-win32-ia32': 0.33.5
            '@img/sharp-win32-x64': 0.33.5

    simple-swizzle@0.2.2:
        dependencies:
            is-arrayish: 0.3.2

    source-map-support@0.5.21:
        dependencies:
            buffer-from: 1.1.2
            source-map: 0.6.1

    source-map@0.6.1: {}

    stoppable@1.1.0: {}

    supports-color@10.0.0: {}

    tslib@2.8.1:
        optional: true

    typescript@5.8.3: {}

    ufo@1.6.1: {}

    undici@5.29.0:
        dependencies:
            '@fastify/busboy': 2.1.1

    unenv@2.0.0-rc.17:
        dependencies:
            defu: 6.1.4
            exsolve: 1.0.7
            ohash: 2.0.11
            pathe: 2.0.3
            ufo: 1.6.1

    workerd@1.20250709.0:
        optionalDependencies:
            '@cloudflare/workerd-darwin-64': 1.20250709.0
            '@cloudflare/workerd-darwin-arm64': 1.20250709.0
            '@cloudflare/workerd-linux-64': 1.20250709.0
            '@cloudflare/workerd-linux-arm64': 1.20250709.0
            '@cloudflare/workerd-windows-64': 1.20250709.0

    wrangler@4.24.1:
        dependencies:
            '@cloudflare/kv-asset-handler': 0.4.0
            '@cloudflare/unenv-preset': 2.3.3(unenv@2.0.0-rc.17)(workerd@1.20250709.0)
            blake3-wasm: 2.1.5
            esbuild: 0.25.4
            miniflare: 4.20250709.0
            path-to-regexp: 6.3.0
            unenv: 2.0.0-rc.17
            workerd: 1.20250709.0
        optionalDependencies:
            fsevents: 2.3.3
        transitivePeerDependencies:
            - bufferutil
            - utf-8-validate

    ws@8.18.0: {}

    youch-core@0.3.3:
        dependencies:
            '@poppinss/exception': 1.2.2
            error-stack-parser-es: 1.0.5

    youch@4.1.0-beta.10:
        dependencies:
            '@poppinss/colors': 4.1.5
            '@poppinss/dumper': 0.6.4
            '@speed-highlight/core': 1.2.7
            cookie: 1.0.2
            youch-core: 0.3.3

    zod@3.22.3: {}
