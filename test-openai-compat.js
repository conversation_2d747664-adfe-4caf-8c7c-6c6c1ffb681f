/**
 * 测试 OpenAI 兼容 API 端点的功能
 * 验证格式转换和错误处理
 */

// 模拟 OpenAI 请求体
const mockOpenAIRequest = {
    model: 'google-ai-studio/gemini-2.5-pro',
    messages: [
        {
            role: 'user',
            content: 'Hello, how are you?'
        }
    ],
    max_tokens: 100,
    temperature: 0.7,
    top_p: 0.9
}

// 模拟 Google AI Studio 响应体
const mockGoogleResponse = {
    candidates: [
        {
            content: {
                parts: [
                    {
                        text: "Hello! I'm doing well, thank you for asking. How can I help you today?"
                    }
                ]
            },
            finishReason: 'STOP'
        }
    ],
    usageMetadata: {
        promptTokenCount: 10,
        candidatesTokenCount: 20,
        totalTokenCount: 30
    }
}

// 导入转换函数（模拟）
function convertOpenAIBodyToGoogleAI(openaiBody) {
    // 验证输入
    if (!openaiBody || typeof openaiBody !== 'object') {
        throw new Error('Invalid openaiBody: must be an object')
    }

    if (!Array.isArray(openaiBody.messages)) {
        throw new Error('Invalid openaiBody: messages must be an array')
    }

    if (openaiBody.messages.length === 0) {
        throw new Error('Invalid openaiBody: messages array cannot be empty')
    }

    // 转换消息格式
    const contents = []
    for (const message of openaiBody.messages) {
        // 验证消息格式
        if (!message || typeof message !== 'object') {
            throw new Error('Invalid message: must be an object')
        }

        if (typeof message.content !== 'string') {
            throw new Error('Invalid message: content must be a string')
        }

        if (!message.role || (message.role !== 'user' && message.role !== 'assistant' && message.role !== 'system')) {
            throw new Error('Invalid message: role must be "user", "assistant", or "system"')
        }

        // 对于单轮对话，使用简单格式（不包含 role）
        // 对于多轮对话，需要包含 role
        if (openaiBody.messages.length === 1) {
            // 单轮对话：使用最简单的格式
            contents.push({
                parts: [{ text: message.content }]
            })
        } else {
            // 多轮对话：包含 role 字段
            contents.push({
                role: message.role === 'assistant' ? 'model' : 'user',
                parts: [{ text: message.content }]
            })
        }
    }

    // 构建 Google AI Studio 请求体
    const googleBody = {
        contents: contents
    }

    // 转换可选参数，验证类型
    if (openaiBody.max_tokens !== undefined) {
        if (typeof openaiBody.max_tokens !== 'number' || openaiBody.max_tokens <= 0) {
            throw new Error('Invalid max_tokens: must be a positive number')
        }
        googleBody.generationConfig = googleBody.generationConfig || {}
        googleBody.generationConfig.maxOutputTokens = openaiBody.max_tokens
    }

    if (openaiBody.temperature !== undefined) {
        if (typeof openaiBody.temperature !== 'number' || openaiBody.temperature < 0 || openaiBody.temperature > 2) {
            throw new Error('Invalid temperature: must be a number between 0 and 2')
        }
        googleBody.generationConfig = googleBody.generationConfig || {}
        googleBody.generationConfig.temperature = openaiBody.temperature
    }

    if (openaiBody.top_p !== undefined) {
        if (typeof openaiBody.top_p !== 'number' || openaiBody.top_p < 0 || openaiBody.top_p > 1) {
            throw new Error('Invalid top_p: must be a number between 0 and 1')
        }
        googleBody.generationConfig = googleBody.generationConfig || {}
        googleBody.generationConfig.topP = openaiBody.top_p
    }

    return googleBody
}

function convertGoogleAIBodyToOpenAI(googleBody, model) {
    // 构建 OpenAI 格式的响应
    const openaiResponse = {
        id: `chatcmpl-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`,
        object: 'chat.completion',
        created: Math.floor(Date.now() / 1000),
        model: `google-ai-studio/${model}`,
        choices: [],
        usage: {
            prompt_tokens: 0,
            completion_tokens: 0,
            total_tokens: 0
        }
    }

    // 转换候选响应
    if (googleBody.candidates && googleBody.candidates.length > 0) {
        for (let i = 0; i < googleBody.candidates.length; i++) {
            const candidate = googleBody.candidates[i]
            const content = candidate.content

            if (content && content.parts && content.parts.length > 0) {
                const text = content.parts.map(part => part.text || '').join('')

                openaiResponse.choices.push({
                    index: i,
                    message: {
                        role: 'assistant',
                        content: text
                    },
                    finish_reason: candidate.finishReason === 'STOP' ? 'stop' : 'length'
                })
            }
        }
    }

    // 如果没有有效的候选响应，创建一个默认响应
    if (openaiResponse.choices.length === 0) {
        openaiResponse.choices.push({
            index: 0,
            message: {
                role: 'assistant',
                content: "I apologize, but I couldn't generate a response."
            },
            finish_reason: 'stop'
        })
    }

    // 尝试提取使用统计信息
    if (googleBody.usageMetadata) {
        openaiResponse.usage.prompt_tokens = googleBody.usageMetadata.promptTokenCount || 0
        openaiResponse.usage.completion_tokens = googleBody.usageMetadata.candidatesTokenCount || 0
        openaiResponse.usage.total_tokens =
            googleBody.usageMetadata.totalTokenCount ||
            openaiResponse.usage.prompt_tokens + openaiResponse.usage.completion_tokens
    }

    return openaiResponse
}

// 运行测试
function runTests() {
    console.log('🧪 测试 OpenAI 兼容 API 格式转换...\n')

    // 测试 1: OpenAI 到 Google AI Studio 转换
    console.log('📤 测试 1: OpenAI → Google AI Studio 转换')
    try {
        const googleBody = convertOpenAIBodyToGoogleAI(mockOpenAIRequest)
        console.log('✅ 转换成功')
        console.log('输入:', JSON.stringify(mockOpenAIRequest, null, 2))
        console.log('输出:', JSON.stringify(googleBody, null, 2))
    } catch (error) {
        console.log('❌ 转换失败:', error.message)
    }

    console.log('\n' + '='.repeat(50) + '\n')

    // 测试 2: Google AI Studio 到 OpenAI 转换
    console.log('📥 测试 2: Google AI Studio → OpenAI 转换')
    try {
        const openaiResponse = convertGoogleAIBodyToOpenAI(mockGoogleResponse, 'gemini-2.5-pro')
        console.log('✅ 转换成功')
        console.log('输入:', JSON.stringify(mockGoogleResponse, null, 2))
        console.log('输出:', JSON.stringify(openaiResponse, null, 2))
    } catch (error) {
        console.log('❌ 转换失败:', error.message)
    }

    console.log('\n' + '='.repeat(50) + '\n')

    // 测试 3: 错误处理
    console.log('🚨 测试 3: 错误处理')

    const invalidRequests = [
        { name: '空对象', data: {} },
        { name: '缺少 messages', data: { model: 'test' } },
        { name: '空 messages 数组', data: { messages: [] } },
        { name: '无效 temperature', data: { messages: [{ role: 'user', content: 'test' }], temperature: 3 } },
        { name: '无效 max_tokens', data: { messages: [{ role: 'user', content: 'test' }], max_tokens: -1 } }
    ]

    for (const testCase of invalidRequests) {
        try {
            convertOpenAIBodyToGoogleAI(testCase.data)
            console.log(`❌ ${testCase.name}: 应该抛出错误但没有`)
        } catch (error) {
            console.log(`✅ ${testCase.name}: 正确捕获错误 - ${error.message}`)
        }
    }

    console.log('\n🎉 所有测试完成！')
}

// 运行测试
runTests()
