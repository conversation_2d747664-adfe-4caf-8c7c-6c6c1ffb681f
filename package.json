{"name": "one-balance", "version": "0.0.0", "license": "MIT", "scripts": {"deploycf": "cp wrangler.jsonc.tpl wrangler.jsonc && node pre-deploy.mjs && wrangler types && pnpm prettier --write . && pnpm migrate:remote && wrangler deploy", "dev": "cp wrangler.jsonc.tpl wrangler.jsonc && wrangler types && pnpm prettier --write . && pnpm migrate && wrangler dev --port 8080", "migrate:remote": "drizzle-kit generate && wrangler d1 migrations apply one-balance --remote", "migrate": "drizzle-kit generate && wrangler d1 migrations apply one-balance --local"}, "packageManager": "pnpm@10.12.2+sha512.a32540185b964ee30bb4e979e405adc6af59226b438ee4cc19f9e8773667a66d302f5bfee60a39d3cac69e35e4b96e708a71dd002b7e9359c4112a1722ac323f", "devDependencies": {"drizzle-kit": "^0.31.2", "prettier": "^3.6.2", "typescript": "^5.5.2", "wrangler": "^4.21.0"}, "dependencies": {"drizzle-orm": "^0.44.2"}}